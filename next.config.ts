import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  output: 'standalone',
  webpack: (config) => {
    config.externals.push({
      'utf-8-validate': 'commonjs utf-8-validate',
      bufferutil: 'commonjs bufferutil',
      canvas: 'commonjs canvas',
    })
    config.resolve.alias.canvas = false
    return config
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  // images: {
  //   remotePatterns: [{ protocol: 'https', hostname: 'cf.shopee.co.id', port: '', pathname: '/**' }],
  // },
}

export default nextConfig
