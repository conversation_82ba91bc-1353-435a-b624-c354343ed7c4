export const BYPASS_SESSION_URL: Array<string> = []

// login (these bypass the proxy and go directly to auth endpoints)
export const VERIFY_OTP = '/api/auth/verify-otp'
export const ADMIN_LOGIN = '/api/account/admin/login'

// news (proxied through /api/proxy)
export const GET_NEWS = 'api/news'
export const CREATE_NEWS = 'api/news'
export const UPDATE_NEWS = 'api/news'
export const DELETE_NEWS = 'api/news'

// news category (proxied through /api/proxy)
export const GET_NEWS_CATEGORY = 'api/news-category'
export const CREATE_NEWS_CATEGORY = 'api/news-category'
export const UPDATE_NEWS_CATEGORY = 'api/news-category'
export const DELETE_NEWS_CATEGORY = 'api/news-category'

// news type (proxied through /api/proxy)
export const GET_NEWS_TYPE = 'api/news-type'
export const CREATE_NEWS_TYPE = 'api/news-type'
export const UPDATE_NEWS_TYPE = 'api/news-type'
export const DELETE_NEWS_TYPE = 'api/news-type'

// job category (proxied through /api/proxy)
export const GET_JOB_CATEGORY = 'api/project-category'
export const CREATE_JOB_CATEGORY = 'api/project-category'
export const UPDATE_JOB_CATEGORY = 'api/project-category'
export const DELETE_JOB_CATEGORY = 'api/project-category'

// skill set (proxied through /api/proxy)
export const GET_SKILL_SET = 'api/skill'
export const CREATE_SKILL_SET = 'api/skill'
export const UPDATE_SKILL_SET = 'api/skill'
export const DELETE_SKILL_SET = 'api/skill'
export const DOWNLOAD_SKILL_SET = 'api/skill/download'

// admin (proxied through /api/proxy)
export const GET_ADMIN = 'api/admin'
export const CREATE_ADMIN = 'api/admin'
export const UPDATE_ADMIN = 'api/admin'
export const DELETE_ADMIN = 'api/admin'

// user (proxied through /api/proxy)
export const GET_USER = 'api/user'
export const DELETE_USER = 'api/user'
