import { LuBanknote, LuClipboardList, LuCog, LuMonitorCog, LuUserRoundCog } from 'react-icons/lu'

export type TMenu = {
  label: string
  href?: string
  icon?: React.ReactNode
  subMenu?: TMenu[]
}

export const Menus: Array<TMenu> = [
  {
    label: 'Pekerjaan',
    icon: <LuClipboardList />,
    subMenu: [
      {
        label: 'List Pekerjaan',
        href: '/list-pekerjaan',
      },
      {
        label: 'List Refund',
        href: '/list-refund',
      },
    ],
  },
  {
    label: 'Keuangan',
    icon: <LuBanknote />,
    subMenu: [
      {
        label: 'List Masuk',
        href: '/list-masuk',
      },
      {
        label: 'List Keluar',
        href: '/list-keluar',
      },
    ],
  },
  {
    label: 'Pengguna',
    icon: <LuUserRoundCog />,
    subMenu: [
      {
        label: 'List Admin',
        href: '/list-admin',
      },
      {
        label: 'List Pekerja',
        href: '/list-pekerja',
      },
      {
        label: 'List Pelanggan',
        href: '/list-pelanggan',
      },
    ],
  },
  {
    label: 'Data Master',
    icon: <LuCog />,
    subMenu: [
      {
        label: 'Keahlian Pekerjaan',
        href: '/keahlian-pekerjaan',
      },
      {
        label: 'Kategori Pekerjaan',
        href: '/kategori-pekerjaan',
      },
      {
        label: 'Berita',
        href: '/berita',
      },
      {
        label: 'Kategori Berita',
        href: '/kategori-berita',
      },
      { label: 'Tipe Berita', href: '/tipe-berita' },
    ],
  },
  {
    label: 'Konfigurasi Aplikasi',
    icon: <LuMonitorCog />,
    href: '/konfigurasi-aplikasi',
  },
]
