import { VERIFY_OTP, ADMIN_LOGIN } from '@/constant/api-url'
import NextAuth, { CredentialsSignin } from 'next-auth'
import Credentials from 'next-auth/providers/credentials'

const deletedField = ['_id', 'createdAt', 'updatedAt']
const userDeleteField = [
  '_id',
  'status',
  'isEmailVerified',
  'isPhoneVerified',
  'createdAt',
  'updatedAt',
]

export class InvalidToken extends CredentialsSignin {
  constructor(message: string = 'Invalid Token') {
    super(message)
    this.name = 'InvalidToken'
    this.code = 'InvalidToken'
  }
}

export class ExpiredToken extends CredentialsSignin {
  constructor(message: string = 'Token Expired') {
    super(message)
    this.name = 'ExpiredToken'
    this.code = 'ExpiredToken'
  }
}

export const { auth, handlers } = NextAuth({
  providers: [
    Credentials({
      id: 'admin-login',
      name: 'Ad<PERSON> Login',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        try {
          const apiBaseUrl =
            process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.goodjob.coniolabs.id'
          const res = await fetch(`${apiBaseUrl}${ADMIN_LOGIN}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: credentials?.email,
              password: credentials?.password,
            }),
          })

          const json = await res.json()

          console.log({ json })

          if (!res.ok) {
            if (res.status === 401) {
              throw new InvalidToken('Invalid email or password')
            }
            if (res.status === 404) {
              throw new InvalidToken('User not found')
            }
            return { errorStatus: res.status + '' }
          }

          if (res.ok && json) {
            // Clean up the response data
            for (const field of deletedField) {
              delete json?.data?.[field]
            }
            if (json.data.user) {
              json.data.user.id = json.data.user._id
              for (const field of userDeleteField) {
                delete json?.data?.user?.[field]
              }
            }
            return json?.data
          }

          return null
        } catch (error) {
          console.error('Admin login authorization error:', error)

          if (error instanceof InvalidToken || error instanceof ExpiredToken) {
            throw error
          }
          return null
        }
      },
    }),
    Credentials({
      id: 'otp-login',
      name: 'OTP Login',
      credentials: {
        identifier: { label: 'Identifier', type: 'text' },
        otp: { label: 'OTP', type: 'text' },
      },
      async authorize(credentials) {
        try {
          const apiBaseUrl =
            process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.goodjob.coniolabs.id'
          const res = await fetch(`${apiBaseUrl}${VERIFY_OTP}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              identifier: credentials?.identifier,
              otp: credentials?.otp,
            }),
          })

          const json = await res.json()

          if (!res.ok) {
            if (res.status === 404) {
              throw new InvalidToken('')
            }
            if (res.status === 400) {
              throw new ExpiredToken('')
            }
            return { errorStatus: res.status + '' }
          }

          if (res.ok && json) {
            for (const field of deletedField) {
              delete json?.data?.[field]
            }
            json.data.user.id = json.data.user._id
            for (const field of userDeleteField) {
              delete json?.data?.user?.[field]
            }
            return json?.data
          }

          return null
        } catch (error) {
          console.error('OTP authorization error:', error)

          if (error instanceof InvalidToken || error instanceof ExpiredToken) {
            throw error
          }
          return null
        }
      },
    }),
  ],
  session: { strategy: 'jwt' as const, maxAge: 12 * 60 * 60, updateAge: 0 },
  jwt: { maxAge: 12 * 60 * 60 },
  secret: process.env.AUTH_SECRET,
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token = { ...token, ...user }
      }
      return token
    },
    async session({ session, token }) {
      return { ...session, user: { ...token } } as never
    },
  },
})
