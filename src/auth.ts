import { VERIFY_OTP } from "@/constant/api-url";
import NextAuth, { CredentialsSignin } from 'next-auth'
import Credentials from 'next-auth/providers/credentials'

const deletedField = ['_id', 'createdAt', 'updatedAt']
const userDeleteField = [
  '_id',
  'status',
  'isEmailVerified',
  'isPhoneVerified',
  'createdAt',
  'updatedAt',
]

export class InvalidToken extends CredentialsSignin {
  constructor(message: string = 'Invalid Token') {
    super(message)
    this.name = 'InvalidToken'
    this.code = 'InvalidToken'
  }
}

export class ExpiredToken extends CredentialsSignin {
  constructor(message: string = 'Token Expired') {
    super(message)
    this.name = 'ExpiredToken'
    this.code = 'ExpiredToken'
  }
}

export const { auth, handlers } = NextAuth({
  providers: [
    Credentials({
      name: 'credentials',
      credentials: {
        identifier: { label: 'Identifier', type: 'text' },
        otp: { label: 'OTP', type: 'text' },
      },
      async authorize(credentials) {
        try {
          const res = await fetch(VERIFY_OTP, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              identifier: credentials?.identifier,
              otp: credentials?.otp,
            }),
          })

          const json = await res.json()

          if (!res.ok) {
            if (res.status === 404) {
              throw new InvalidToken('')
            }
            if (res.status === 400) {
              throw new ExpiredToken('')
            }
            // console.log('disinikan ?')
            // throw new AuthError(res.status + '')
            return { errorStatus: res.status + '' }
          }

          if (res.ok && json) {
            for (const field of deletedField) {
              delete json?.data?.[field]
            }
            json.data.user.id = json.data.user._id
            for (const field of userDeleteField) {
              delete json?.data?.user?.[field]
            }
            return json?.data
          }

          return null
        } catch (error) {
          console.error('Authorization error:', error)

          if (error instanceof InvalidToken || error instanceof ExpiredToken) {
            throw error
          }
          // throw new Error('Failed to authenticate')
          return null
        }
      },
    }),
  ],
  session: { strategy: 'jwt' as const, maxAge: 12 * 60 * 60, updateAge: 0 },
  jwt: { maxAge: 12 * 60 * 60 },
  secret: process.env.AUTH_SECRET,
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token = { ...token, ...user }
      }
      return token
    },
    async session({ session, token }) {
      return { ...session, user: { ...token } } as never
    },
  },
})
