'use client'

import { <PERSON><PERSON><PERSON>t, CircleXIcon, Loader2 } from 'lucide-react'

import { useConfirm } from '@/hooks/use-confirm'
import { cn } from '@/lib/utils'
import { useEffect } from 'react'
import { FaRegCircleCheck } from 'react-icons/fa6'
import { Button } from '../ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'

function ModalConfirm() {
  const { isOpen, message, toogle, isLoading } = useConfirm()

  const MessageConfirm = () => {
    return (
      <DialogContent
        className='sm:max-w-[604px]'
        onInteractOutside={(e) => e.preventDefault()}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <div className='flex flex-col items-center justify-center gap-2'>
          {message.icon || (
            <CircleAlert
              width={50}
              height={50}
              fill='#FFBF45'
              className={cn('text-white', message.iconStyle)}
            />
          )}

          <DialogHeader>
            <DialogTitle className='text-base text-[#3A3541DE]'>{message.title}</DialogTitle>
          </DialogHeader>
          <DialogDescription className='mt-2 w-[350px] px-4 text-center text-sm text-[#3A3541DE]'>
            {message.description}
          </DialogDescription>
          <DialogFooter className='mt-7 flex gap-[24px]'>
            <Button
              loading={isLoading}
              onClick={message.onConfirm}
              type='button'
              className={cn('min- h-[30px] w-[100px] shadow-md', message.confirmButtonStyle)}
              variant={message.confirmButtonVariant || 'default'}
              disabled={isLoading}
            >
              {message.confirmText}
            </Button>

            <DialogClose asChild>
              <Button
                disabled={isLoading}
                type='button'
                className='h-[30px] w-[100px] bg-[#EEEEEE] shadow-md'
                variant='outline'
              >
                {message.cancelText || 'Tidak'}
              </Button>
            </DialogClose>
          </DialogFooter>
        </div>
      </DialogContent>
    )
  }
  const MessageNoticeSuccess = () => {
    return (
      <DialogContent
        className='h-[289px] sm:max-w-[604px]'
        onInteractOutside={(e) => {
          e.preventDefault()
        }}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <div className='flex flex-col items-center justify-center gap-2'>
          <FaRegCircleCheck className='mx-auto size-[64px] text-green-500' />
          <DialogDescription className='mt-2 w-[350px] px-4 text-center text-base font-bold text-[#3A3541DE]'>
            {message.description}
          </DialogDescription>
        </div>
      </DialogContent>
    )
  }
  const MessageNoticeError = () => {
    return (
      <DialogContent
        showCloseButton
        className='h-[289px] sm:max-w-[604px]'
        onInteractOutside={(e) => {
          e.preventDefault()
        }}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <div className='flex flex-col items-center justify-center gap-0'>
          <CircleXIcon className='mx-auto size-[64px] text-red-500' />
          <DialogTitle className='mt-1 text-base text-red-500'>{message.title}</DialogTitle>
          <DialogDescription className='mt-2 w-[350px] px-4 text-center text-base font-normal text-[#3A3541DE]'>
            {message.description}
          </DialogDescription>
        </div>
      </DialogContent>
    )
  }

  const MessageLoading = () => (
    <DialogContent
      className='flex h-[200px] items-center justify-center sm:max-w-[604px]'
      onInteractOutside={(e) => e.preventDefault()}
      onOpenAutoFocus={(e) => e.preventDefault()}
    >
      <Loader2 className='animate-spin' size={48} />
      {message.description}
    </DialogContent>
  )

  useEffect(() => {
    if (isOpen && message.duration) {
      setTimeout(() => toogle(), message.duration)
    }
  }, [message.duration, toogle, isOpen])

  return (
    <>
      <Dialog onOpenChange={toogle} open={isOpen}>
        {message.type === 'confirm' && <MessageConfirm />}
        {message.type === 'notice-success' && <MessageNoticeSuccess />}
        {message.type === 'notice-error' && <MessageNoticeError />}
        {message.type === 'loading' && <MessageLoading />}
      </Dialog>
    </>
  )
}

export default ModalConfirm
