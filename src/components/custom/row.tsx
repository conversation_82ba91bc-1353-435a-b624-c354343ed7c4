import { cn } from '@/lib/utils'
import { ReactNode } from 'react'

const Row = ({
  className,
  label,
  value,
}: {
  label: string
  value: string | ReactNode
  className?: { label?: string; value?: string; container?: string }
}) => {
  return (
    <div className={cn('gap-1 md:flex', className?.container)}>
      <p className={cn('min-w-[150px] font-semibold', className?.label)}>{label}</p>
      <p>:</p>
      <div className={className?.value}>{value}</div>
    </div>
  )
}

export { Row }
