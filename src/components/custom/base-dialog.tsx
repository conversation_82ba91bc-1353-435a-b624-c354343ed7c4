'use client'

import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog'
import { useDialog } from '@/hooks/use-dialog'

const BaseDialog = () => {
  const { toggle, size, title, content, open, disableBackdrop, withClose, onClose } = useDialog()

  return (
    <Dialog open={open} onOpenChange={toggle}>
      <DialogContent
        className={`!max-w-screen-${size}`}
        onInteractOutside={disableBackdrop ? (e) => e.preventDefault() : undefined}
        onEscapeKeyDown={disableBackdrop ? (e) => e.preventDefault() : undefined}
        showCloseButton={withClose}
      >
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        {content}
      </DialogContent>
    </Dialog>
  )
}

export default BaseDialog
