import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar'
import { TMenu } from '@/constant/menus'
import { cn } from '@/lib/utils'
import { ChevronRight, CircleIcon } from 'lucide-react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'

const hasActiveDescendant = (menu: TMenu, currentPath: string): boolean => {
  if (menu.href === currentPath) return true

  if (menu.subMenu && menu.subMenu.length > 0) {
    for (const subMenu of menu.subMenu) {
      if (hasActiveDescendant(subMenu, currentPath)) return true
    }
  }

  return false
}

const MenuBuilder = ({
  menu,
  level = 0,
  currentPath,
}: {
  menu: TMenu
  level?: number
  currentPath: string
}) => {
  const params = useParams()

  Object.keys(params).map((key) => {
    currentPath = currentPath.replace(`${params[key]}`, '')
  })

  currentPath = currentPath?.replace(/\/$/, '')

  const isCurrentActive = currentPath === menu.href
  const hasActiveDesc = hasActiveDescendant(menu, currentPath)

  const [isOpen, setIsOpen] = useState(hasActiveDesc)

  useEffect(() => {
    setIsOpen(hasActiveDesc)
  }, [hasActiveDesc])

  if (!menu.subMenu || menu.subMenu.length === 0) {
    return (
      <SidebarMenuItem key={menu.label}>
        <SidebarMenuButton
          className={cn(
            'text-left text-base !font-normal',
            `menu-${level + 1}`,
            level > 0 && `pl-${4 + level * 2} text-base group-data-[collapsible=icon]:hidden`,
            isCurrentActive &&
              '!bg-main/70 !text-main-foreground text-accent-foreground !font-semibold'
          )}
          tooltip={menu.label}
          isActive={isCurrentActive}
        >
          <Link className='flex w-full gap-2 [&>svg]:size-6' href={menu.href || ''}>
            {level === 0
              ? menu.icon
              : menu?.icon || <CircleIcon fill='none' className='!size-2 text-transparent' />}
            <span className='group-data-[collapsible=icon]:hidden'>{menu.label}</span>
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Collapsible
      key={menu.label}
      className={cn(
        `group/menu-${level + 1}`,
        level > 0 && 'group-data-[collapsible=icon]:hidden hover:cursor-pointer'
      )}
      open={isOpen}
      onOpenChange={setIsOpen}
      asChild
    >
      <SidebarMenuItem className={level === 0 ? 'px-0' : `pl-${2 + level * 2}`}>
        <CollapsibleTrigger
          className={cn(
            'flex w-full justify-between gap-2 py-1.5 !font-normal !text-black hover:cursor-pointer',
            `group/menu-${level + 1} menu-dropdown-${level + 1}`
          )}
          title={menu.label}
          asChild
        >
          <SidebarMenuButton tooltip={menu.label}>
            <div
              className={cn(
                'flex items-center gap-2 text-base hover:cursor-pointer [&>svg]:size-6 group-data-[collapsible=icon]:[&>svg]:size-4',
                level > 0 && 'text-base'
              )}
            >
              {level === 0
                ? menu.icon
                : menu?.icon || <CircleIcon fill='none' className='!size-2 text-transparent' />}
              <span className='group-data-[collapsible=icon]:hidden'>{menu.label}</span>
            </div>
            <ChevronRight
              className={cn(
                'ml-auto transition-transform duration-300',
                `group/menu-${level + 1} group-data-[state=open]/menu-${level + 1}:rotate-90`
              )}
            />
          </SidebarMenuButton>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <SidebarGroupContent className={level > 0 ? `pl-${2 + level * 2}` : ''}>
            <SidebarMenu>
              {menu.subMenu.map((subMenu) => (
                <MenuBuilder
                  key={subMenu.label}
                  menu={subMenu}
                  level={level + 1}
                  currentPath={currentPath}
                />
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </CollapsibleContent>
      </SidebarMenuItem>
    </Collapsible>
  )
}

export { MenuBuilder }
