import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'

const UploadFile = ({
  withPreview,
  onChange,
  value,
  classNames,
  ...props
}: React.ComponentProps<'input'> & { withPreview?: boolean; classNames?: Record<string, any> }) => {
  const [file, setFile] = useState<File | null | string>(value as any)

  useEffect(() => {
    setFile(value as any)
  }, [value])

  console.log({ value })

  return (
    <div className={cn('h-30 w-full rounded-md', classNames?.container)}>
      <label
        htmlFor='file-upload'
        className={cn(
          'dark:hover:bg-bray-800 flex h-full w-full cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 dark:border-gray-600 dark:bg-gray-700 dark:hover:border-gray-500 dark:hover:bg-gray-600',
          file && 'border-none',
          classNames?.label
        )}
      >
        {withPreview && file && (
          <img
            src={file instanceof File ? URL.createObjectURL(file) : (file as string)}
            alt=''
            className={cn('h-30 w-full rounded-lg object-cover', classNames?.image)}
          />
        )}
        {!file && (
          <p className='mb-2 text-sm text-gray-500 dark:text-gray-400'>
            <span className='font-semibold'>Click to upload</span> or drag and drop
          </p>
        )}
      </label>
      <input
        id='file-upload'
        type='file'
        className='hidden'
        onChange={(e) => {
          setFile(e.target?.files?.[0] || null)
          onChange?.(e.target?.files?.[0] as any)
        }}
        {...props}
      />
    </div>
  )
}

export { UploadFile }
