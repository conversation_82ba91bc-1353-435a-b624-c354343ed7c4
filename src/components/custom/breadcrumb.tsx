'use client'

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { usePathname } from 'next/navigation'
import { Fragment } from 'react'
import { FaHome } from 'react-icons/fa'

const CustomBreadcrumb = () => {
  const path = usePathname()
  const pathArray = path.split('/').filter((val) => !!val)

  const items: Array<{ path: string; label: string }> = []

  for (let i = 0; i < pathArray.length; i++) {
    let path = ''
    const label = pathArray[i].replace(/-/g, ' ')

    for (let j = 0; j <= i; j++) {
      path += '/' + pathArray[j]
    }

    items.push({ path, label })
  }

  return (
    <Breadcrumb className='mt-3 mb-2'>
      <BreadcrumbList>
        <BreadcrumbItem className='hidden md:block'>
          <BreadcrumbLink href='#'>
            <FaHome />
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator className='hidden md:block' />
        {items.map((item, i) => {
          return (
            <Fragment key={i}>
              <BreadcrumbItem
                className={i !== pathArray.length - 1 ? 'hidden md:block' : ''}
                key={i}
              >
                <BreadcrumbLink
                  href={i !== pathArray.length - 1 ? item.path : ''}
                  className='capitalize'
                >
                  {item.label}
                </BreadcrumbLink>
              </BreadcrumbItem>
              {i !== pathArray.length - 1 && <BreadcrumbSeparator className='hidden md:block' />}
            </Fragment>
          )
        })}
      </BreadcrumbList>
    </Breadcrumb>
  )
}

export default CustomBreadcrumb
