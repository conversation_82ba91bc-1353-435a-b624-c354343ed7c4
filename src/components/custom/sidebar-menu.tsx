'use client'
import { MenuBuilder } from '@/components/custom/menu-builder'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar'
import { Menus } from '@/constant/menus'
import { useIsMobile } from '@/hooks/use-mobile'
import { successToast } from '@/lib/toast'
import { cn } from '@/lib/utils'
import { SettingsIcon } from 'lucide-react'
import { signOut, useSession } from 'next-auth/react'
import Image from 'next/image'
import { redirect, usePathname, useRouter } from 'next/navigation'
import { LuLogOut, LuUserRound } from 'react-icons/lu'
import '@/app/css/menu.css'

const SidebarCustomMenu = () => {
  const router = useRouter()
  const session = useSession()
  const isMobile = useIsMobile()
  const path = usePathname()
  const { state } = useSidebar()
  const user = session?.data?.user?.user

  const handleSignOut = async () => {
    await signOut()
    successToast('Logged out successfully')
    redirect('/login')
  }

  const handleChangeProfile = () => {
    router.push('/profile')
  }

  return (
    <Sidebar collapsible='icon' className='select-none'>
      <SidebarHeader className='overflow-x-hidden'>
        <div className='flex items-center gap-2'>
          <Image
            src='/images/common/logo_light.png'
            className='mx-auto'
            alt='logo'
            width={70}
            height={40}
          />
          <div className='text-main text-lg font-bold'>Good Job CMS</div>
        </div>
      </SidebarHeader>
      <SidebarContent className='mt-10 px-1 group-data-[collapsible=icon]:mt-0'>
        <SidebarMenu>{Menus.map((menu) => MenuBuilder({ menu, currentPath: path }))}</SidebarMenu>
      </SidebarContent>
      <SidebarFooter className={cn('overflow-x-hidden', state === 'collapsed' && 'mx-auto')}>
        <DropdownMenu>
          <DropdownMenuTrigger
            className='flex w-full items-center justify-between gap-2 hover:cursor-pointer'
            asChild
          >
            <SidebarMenuButton className={state === 'expanded' ? 'bg-gray-200' : ''} size='lg'>
              <div className='flex items-center gap-2'>
                <Avatar>
                  <AvatarImage src='/anonymous.jpeg' />
                  <AvatarFallback>
                    {user?.name
                      ?.split(' ')
                      ?.map((word) => word[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>
                {state === 'expanded' && <div className='text-gray-500'>{user?.name}</div>}
              </div>
              {state === 'expanded' && <SettingsIcon size={20} className='text-gray-500' />}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className='max-w-[300px] truncate overflow-x-hidden'
            side={isMobile ? 'bottom' : 'right'}
            sideOffset={4}
            align='end'
          >
            <DropdownMenuItem className='items-center text-sm' onClick={handleChangeProfile}>
              <LuUserRound />
              <span>Ubah Profile</span>
            </DropdownMenuItem>
            <DropdownMenuItem className='items-center text-sm' onClick={handleSignOut}>
              <LuLogOut />
              <span>Keluar</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}

export default SidebarCustomMenu
