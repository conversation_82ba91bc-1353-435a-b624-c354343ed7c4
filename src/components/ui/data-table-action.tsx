import { cn } from '@/lib/utils'
import { DotsHorizontalIcon } from '@radix-ui/react-icons'
import React from 'react'
import { Button } from '../ui/button'
import { Dialog } from '../ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu'

interface ListProps {
  label: string
  value: string
  disabled?: boolean
  icon?: React.ReactNode
  className?: string
}

interface Props {
  list: ListProps[]
  onChange: (value: string) => void
  className?: string
}

export default function DataTableAction({ list, onChange, className }: Props) {
  return (
    <Dialog>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant='ghost'
            className={cn('flex h-8 w-8 p-0 data-[state=open]:bg-muted', className)}
          >
            <DotsHorizontalIcon className='mx-auto h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end' className='w-[160px]'>
          {list.map((item, idx) => (
            <DropdownMenuItem
              className={cn('justify-between hover:cursor-pointer', item.className)}
              key={idx}
              onClick={() => onChange(item.value)}
              disabled={item.disabled ?? false}
            >
              {item.label}
              {item.icon}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </Dialog>
  )
}
