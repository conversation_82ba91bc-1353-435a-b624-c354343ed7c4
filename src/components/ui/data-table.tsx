'use client'

import { Loading } from '@/components/ui/loading'
import {
  Table as TableView,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { IPagination } from '@/hooks/use-pagination'
import { ISorting } from '@/hooks/use-sorting'
import { cn } from '@/lib/utils'
import {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  Table,
  useReactTable,
} from '@tanstack/react-table'
import { DataTablePagination } from './data-table-pagination'
import { DataTableToolbar } from './data-table-toolbar'

interface DataTableProps<TData, TValue> {
  tableData: Record<string, any>
  columns: ColumnDef<TData, TValue>[]
  loading: boolean
  searchField?: string
  filterFields?: string[]
  customDataTableToolbar?: (table: Table<TData>) => React.ReactNode
  paramsHandler?: React.Dispatch<React.SetStateAction<any>>
  paginationState: IPagination
  expandedState?: ExpandedState
  sortingState: Array<ISorting>
  subRowHandler?: (originalRow: TData, index: number) => TData[] | undefined
  pageHandler: React.Dispatch<React.SetStateAction<IPagination>>
  expandedHandler?: React.Dispatch<React.SetStateAction<{}>>
  sortingHandler: React.Dispatch<React.SetStateAction<Array<ISorting>>>
  action?: {
    label: string
    field: (param: TData) => React.ReactNode
  }
  enableRowAction?: boolean
  onRowAction?: (params: TData) => void
  showPagination?: boolean
  autosort?: boolean
  enableAlternatingRowColors?: boolean
  tableHeaderStyle?: string
  tableHeadTextColor?: string
  columnFilters?: ColumnFiltersState
  onColumnFiltersChange?: React.Dispatch<React.SetStateAction<ColumnFiltersState>>
  className?: Record<string, string>
}

export function DataTable<TData, TValue>(props: DataTableProps<TData, TValue>) {
  const {
    columns,
    loading,
    pageHandler,
    sortingHandler,
    tableData,
    customDataTableToolbar,
    filterFields,
    paramsHandler,
    expandedHandler,
    subRowHandler,
    searchField,
    paginationState,
    expandedState,
    sortingState,
    action,
    columnFilters,
    onColumnFiltersChange,
    enableRowAction = false,
    onRowAction,
    showPagination = true,
    autosort = false,
    enableAlternatingRowColors = false,
    tableHeaderStyle = `bg-main`,
    tableHeadTextColor = `main-foreground`,
    className,
  } = props

  const table = useReactTable({
    data: tableData?.data || [],
    columns,
    state: {
      sorting: sortingState,
      pagination: paginationState,
      expanded: expandedState,
      columnFilters,
    },
    enableRowSelection: true,
    manualFiltering: true,
    manualSorting: !autosort,
    getSortedRowModel: getSortedRowModel(),
    manualPagination: true,
    pageCount: tableData?.total_page,
    rowCount: tableData?.total_data,
    onSortingChange: sortingHandler,
    onPaginationChange: pageHandler,
    onExpandedChange: expandedHandler,
    onColumnFiltersChange,
    getCoreRowModel: getCoreRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getSubRows: subRowHandler,
    getFilteredRowModel: getFilteredRowModel(),
  })
  if (loading) {
    return (
      <div className='mt-10'>
        <Loading label='Memuat Data...' />
      </div>
    )
  }

  return (
    <div className='w-full space-y-4'>
      {!columnFilters && !onColumnFiltersChange ? (
        customDataTableToolbar ? (
          customDataTableToolbar(table)
        ) : (
          <DataTableToolbar table={table} searchField={searchField} filterFields={filterFields} />
        )
      ) : null}

      <div className='rounded-md border'>
        <TableView>
          <TableHeader className={tableHeaderStyle}>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className='hover:bg-main hover:cursor-default'>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      className={`text-${tableHeadTextColor}`}
                      key={header.id}
                      style={{
                        minWidth: header.getSize() !== 150 ? header.getSize() : undefined,
                        maxWidth: header.getSize() !== 150 ? header.getSize() : undefined,
                      }}
                      colSpan={header.colSpan}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </TableHead>
                  )
                })}
                {action?.label ? (
                  <TableHead
                    className={`w-28 min-w-28 font-bold capitalize text-${tableHeadTextColor}`}
                  >
                    {action.label}
                  </TableHead>
                ) : null}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {columnFilters && onColumnFiltersChange && (
              <TableRow className='bg-white'>
                {table.getHeaderGroups()[0].headers.map((header) => {
                  const isFilterable = header.column.getCanFilter()
                  return (
                    <TableCell key={header.id} className='p-2'>
                      {isFilterable && (
                        <input
                          type='text'
                          placeholder={`Cari`}
                          value={(header.column.getFilterValue() as string) ?? ''}
                          onChange={(e) => header.column.setFilterValue(e.target.value)}
                          className='w-full rounded border px-2 py-1 text-sm'
                        />
                      )}
                    </TableCell>
                  )
                })}
                {action?.label && <TableCell />}
              </TableRow>
            )}
            {!loading && table.getRowModel().rows?.length > 0 && (
              <>
                {table.getRowModel().rows.map((row, idx) => (
                  <TableRow
                    // @ts-ignore
                    key={subRowHandler ? row.id : (row.original?.id ?? idx)}
                    data-state={row.getIsSelected() && 'selected'}
                    className={cn(
                      enableAlternatingRowColors ? 'odd:bg-gray-100' : '',
                      className?.row
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        style={{
                          width:
                            (cell.column.columnDef.maxSize ?? 0) < (cell.column.columnDef.size ?? 0)
                              ? `${cell.column.columnDef.maxSize}px`
                              : undefined,
                        }}
                        onClick={() => {
                          if (enableRowAction && onRowAction) onRowAction(row.original)
                        }}
                        className={cn(
                          enableRowAction ? 'hover:cursor-pointer' : '',
                          className?.cell
                        )}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </TableCell>
                    ))}
                    {action?.field ? (
                      <TableCell className='text-center'>{action.field(row.original)}</TableCell>
                    ) : null}
                  </TableRow>
                ))}
              </>
            )}

            {!columnFilters && !onColumnFiltersChange
              ? !loading &&
                !table.getRowModel().rows?.length && (
                  <TableRow>
                    <TableCell
                      colSpan={table.getAllColumns().length + (action ? 1 : 0)}
                      className='h-24 text-center'
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )
              : null}
          </TableBody>
        </TableView>
      </div>
      {showPagination && <DataTablePagination table={table} />}
    </div>
  )
}
