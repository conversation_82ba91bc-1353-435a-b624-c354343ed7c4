import * as React from 'react'

import { cn } from '@/lib/utils'

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  isError?: boolean
  suffixIcon?: React.ReactNode
  prefixIcon?: React.ReactNode
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, isError, suffixIcon, prefixIcon, ...props }, ref) => {
    return (
      <>
        <div
          className={cn(
            `flex h-10 w-full rounded-md border ${isError ? 'border-2 border-rose-300' : 'border-input'} ${props.disabled ? 'bg-gray-300' : 'bg-background'} ring-offset-background placeholder:text-muted-foreground items-center px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-base file:font-medium disabled:cursor-not-allowed disabled:opacity-50`,
            'focus-within:border-ring focus-within:ring-ring/50 focus-within:ring-[3px]',
            className
          )}
        >
          {prefixIcon}
          <input
            type={type}
            className='w-full bg-transparent focus:outline-none [&::-webkit-inner-spin-button]:appearance-none'
            ref={ref}
            {...props}
          />
          {suffixIcon}
        </div>

        {isError ? <div className='text-base text-red-500'>{isError} </div> : null}
      </>
    )
  }
)

Input.displayName = 'Input'

export { Input }
