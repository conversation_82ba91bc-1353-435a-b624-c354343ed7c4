import { cn } from '@/lib/utils'
import * as React from 'react'

interface Props {
  label: string
  inputStyle?: string
  labelStyle?: string
  prefixStyle?: string
  prefixContent?: React.ReactNode | string
  suffixContent?: React.ReactNode | string
  withAsterisk?: boolean
  isFocused?: boolean
}

export type InputProps = React.InputHTMLAttributes<HTMLInputElement> & Props

const FloatingInput = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      labelStyle,
      inputStyle,
      type,
      id,
      label,
      prefixContent,
      suffixContent,
      prefixStyle,
      className,
      withAsterisk,
      ...props
    },
    ref
  ) => {
    return (
      <div className='relative flex'>
        <div
          className={cn(
            'input-wrapper flex w-full flex-row rounded-lg border bg-white text-base text-gray-900 focus-within:border-2 focus-within:border-blue-600 dark:border-gray-600 dark:text-white',
            className
          )}
        >
          <input
            type={type ?? 'text'}
            id={id}
            className={cn(
              'prefix peer h-11 w-full rounded-lg px-4 py-2 hover:cursor-text placeholder-shown:hover:cursor-default focus:border-none focus:hover:cursor-text focus-visible:outline-none',
              inputStyle
            )}
            ref={ref}
            {...props}
          />
          {prefixContent && (
            <div
              className={cn(
                'prefix my-auto block pl-3 peer-placeholder-shown:hidden peer-focus:block',
                prefixStyle
              )}
            >
              {prefixContent}
            </div>
          )}
          {suffixContent && (
            <div className='suffix my-auto block pr-2.5 peer-placeholder-shown:hidden peer-focus:block'>
              {suffixContent}
            </div>
          )}
          <label
            htmlFor={id}
            className={cn(
              'bg-background absolute start-1 top-2 z-10 origin-[0] -translate-y-4 scale-75 transform px-2 text-base text-gray-500 duration-300 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:scale-100 peer-focus:top-2 peer-focus:-translate-y-4 peer-focus:scale-75 peer-focus:px-2 peer-focus:text-blue-600 rtl:peer-focus:left-auto rtl:peer-focus:translate-x-1/4 dark:bg-gray-900 dark:text-gray-400 peer-focus:dark:text-blue-500',
              labelStyle,
              props.required || withAsterisk
                ? "after:hidden after:text-red-500 after:content-['*'] peer-focus:after:inline"
                : '',
              props.value && withAsterisk ? 'after:inline' : '',
              props.isFocused
                ? "!top-2 !-translate-y-4 !scale-75 !px-2 after:inline after:text-red-500 after:content-['*']"
                : ''
            )}
          >
            {label}
          </label>
        </div>
      </div>
    )
  }
)
FloatingInput.displayName = 'FloatingInput'

export { FloatingInput }
