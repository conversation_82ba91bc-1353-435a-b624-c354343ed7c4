import { ArrowDownIcon, ArrowUpIcon, CaretSortIcon } from '@radix-ui/react-icons'
import { Column } from '@tanstack/react-table'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface DataTableColumnHeaderProps<TData, TValue> extends React.HTMLAttributes<HTMLDivElement> {
  column: Column<TData, TValue>
  title: string
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  const isSorted = column.getIsSorted()

  return (
    <div className={cn('flex flex-col space-y-1', className)}>
      <div className='flex items-center space-x-2'>
        {column.getCanSort() ? (
          <Button
            variant='ghost'
            size='sm'
            className='-ml-3 h-8 data-[state=open]:bg-accent'
            onClick={column.getToggleSortingHandler()}
          >
            <span className='font-bold capitalize'>{title}</span>
            {isSorted === 'desc' ? (
              <ArrowDownIcon className='ml-2 h-4 w-4' />
            ) : isSorted === 'asc' ? (
              <ArrowUpIcon className='ml-2 h-4 w-4' />
            ) : (
              <CaretSortIcon className='ml-2 h-4 w-4' />
            )}
          </Button>
        ) : (
          <span className='font-bold capitalize'>{title}</span>
        )}
      </div>
    </div>
  )
}
