.group-data-\[state\=open\]\/menu-1\:rotate-90:is(:where(.group\/menu-1)[data-state="open"] *) {
  transform: rotate(90deg);
}
.group-data-\[state\=open\]\/menu-2\:rotate-90:is(:where(.group\/menu-2)[data-state="open"] *) {
    transform: rotate(90deg);
}
.group-data-\[state\=open\]\/menu-3\:rotate-90:is(:where(.group\/menu-3)[data-state="open"] *) {
    transform: rotate(90deg);
}
.menu-2 {
    padding-left: 1.5rem;
}
.menu-dropdown-2 {
    padding-left: 0;
}

.group-data-\[collapsible\=icon\]\:menu\!:is(:where(.group)[data-collapsible="icon"] *) {
    width: 100% !important;
    height: auto !important;
}