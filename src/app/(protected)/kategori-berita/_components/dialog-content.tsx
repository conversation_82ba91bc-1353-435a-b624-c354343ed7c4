'use client'

import {
  AddNewsCategorySchema,
  AddNewsCategorySchemaType,
  EditNewsCategorySchema,
  EditNewsCategorySchemaType,
} from '@/app/(protected)/kategori-berita/_components/schema'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loading } from '@/components/ui/loading'
import { CREATE_NEWS_CATEGORY, GET_NEWS_CATEGORY } from '@/constant/api-url'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { errorToast, successToast } from '@/lib/toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'

const DialogContentNewsCategory = ({ id }: { id?: string }) => {
  const { hide, onClose } = useDialog()

  const { data, isLoading } = useQuery({
    queryKey: ['news-category-detail', id],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          if (!id) return {}

          const res = await axios.get(GET_NEWS_CATEGORY + '/' + id)
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: AddNewsCategorySchemaType | EditNewsCategorySchemaType) => {
      return fetcher({
        callback: async (axios) => {
          const res = await axios.post(CREATE_NEWS_CATEGORY, data)
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
    onSuccess: () => {
      successToast(`Berhasil ${form.getValues('id') ? 'menambahkan' : 'mengubah'} data`)
      hide()
      onClose?.()
    },
    onError: (err) => {
      console.log(err)
      errorToast('Terjadi kesalahan. Silahkan coba lagi.')
    },
  })

  const form = useForm<AddNewsCategorySchemaType | EditNewsCategorySchemaType>({
    resolver: zodResolver(id ? EditNewsCategorySchema : AddNewsCategorySchema),
    defaultValues: {
      id: id ? +id : undefined,
      name: data?.name || '',
    },
  })

  const onSubmit = (data: AddNewsCategorySchemaType | EditNewsCategorySchemaType) => {
    mutate(data)
  }

  if (isLoading) return <Loading label='Memuat data...' />

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-3'>
        <FormField
          control={form.control}
          name='name'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='name' required>
                      Nama Kategori Berita
                    </Label>
                    <Input
                      className='mt-1'
                      id='name'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <div className='mt-4 flex justify-end gap-2'>
          <Button type='submit' loading={isPending}>
            Simpan
          </Button>
          <Button type='button' variant='destructive' onClick={hide} loading={isPending}>
            Batal
          </Button>
        </div>
      </form>
    </Form>
  )
}

export default DialogContentNewsCategory
