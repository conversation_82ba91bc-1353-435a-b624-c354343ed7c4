import { Validation } from '@/constant/validation'
import { z } from 'zod'

export const AddNewsCategorySchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export type AddNewsCategorySchemaType = z.infer<typeof AddNewsCategorySchema>

export const EditNewsCategorySchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export type EditNewsCategorySchemaType = z.infer<typeof EditNewsCategorySchema>
