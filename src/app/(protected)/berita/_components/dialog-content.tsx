'use client'

import {
  AddNewsSchema,
  NewsType,
  EditNewsSchema,
} from '@/app/(protected)/berita/_components/schema'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loading } from '@/components/ui/loading'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  CREATE_NEWS,
  GET_NEWS,
  GET_NEWS_CATEGORY,
  GET_NEWS_TYPE,
  UPDATE_NEWS,
} from '@/constant/api-url'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { errorToast, successToast } from '@/lib/toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'

const DialogContentNews = ({ id }: { id?: string }) => {
  const { hide, onClose } = useDialog()

  const form = useForm<NewsType>({
    resolver: zodResolver(id ? EditNewsSchema : AddNewsSchema),
  })

  const { isLoading } = useQuery({
    queryKey: ['news-detail', id],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          if (!id) return {}

          const res = await axios.get(GET_NEWS + '/' + id)
          const data = res?.data?.data || {}

          form.reset({
            id: data.id,
            name: data.name,
            type: data.type,
            category: data.category,
            description: data.description,
          })

          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { data: newsCategory, isLoading: isLoadingNewsCategory } = useQuery({
    queryKey: ['news-category'],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          const res = await axios.get(GET_NEWS_CATEGORY)
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { data: newsType, isLoading: isLoadingNewsType } = useQuery({
    queryKey: ['news-type'],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          const res = await axios.get(GET_NEWS_TYPE)
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: NewsType) => {
      return fetcher({
        callback: async (axios) => {
          let res
          if (data?.id) {
            res = await axios.put(UPDATE_NEWS + '/' + data.id, data)
          } else {
            res = await axios.post(CREATE_NEWS, data)
          }
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
    onSuccess: () => {
      console.log(form.getValues())
      successToast(`Berhasil ${form.getValues()?.id ? 'mengubah' : 'menambah'} data`)
      hide()
      onClose?.()
    },
    onError: (err) => {
      console.log(err)
      errorToast('Terjadi kesalahan. Silahkan coba lagi.')
    },
  })

  const onSubmit = (data: NewsType) => {
    mutate(data)
  }

  if (isLoading) return <Loading label='Memuat data...' />

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-3'>
        <FormField
          control={form.control}
          name='name'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='name' required>
                      Judul Berita
                    </Label>
                    <Input
                      className='mt-1'
                      id='name'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='category'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label required>Kategori Berita</Label>
                    <Select
                      value={field.value}
                      onValueChange={(value) => field.onChange(value)}
                      disabled={isLoadingNewsCategory}
                    >
                      <SelectTrigger className='mt-1 w-full' isError={!!fieldState.error?.message}>
                        <SelectValue placeholder='Pilih Kategori' />
                      </SelectTrigger>
                      <SelectContent>
                        {newsCategory?.data?.map((d: any) => (
                          <SelectItem key={d.id} value={String(d.id)}>
                            {d.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='type'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label required>Tipe Berita</Label>
                    <Select
                      value={field.value}
                      onValueChange={(value) => field.onChange(value)}
                      disabled={isLoadingNewsType}
                    >
                      <SelectTrigger className='mt-1 w-full' isError={!!fieldState.error?.message}>
                        <SelectValue placeholder='Pilih Tipe' />
                      </SelectTrigger>
                      <SelectContent>
                        {newsType?.data?.map((d: any) => (
                          <SelectItem key={d.id} value={String(d.id)}>
                            {d.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='description'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='description' required>
                      Deskripsi Berita
                    </Label>
                    <Textarea
                      className='mt-1 min-h-24'
                      id='description'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <div className='mt-4 flex justify-end gap-2'>
          <Button type='submit' loading={isPending}>
            Simpan
          </Button>
          <Button type='button' variant='destructive' onClick={hide} loading={isPending}>
            Batal
          </Button>
        </div>
      </form>
    </Form>
  )
}

export default DialogContentNews
