import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { ColumnDef } from '@tanstack/react-table'

export const newsColumns: ColumnDef<Record<string, any>>[] = [
  {
    id: 'id',
    accessorKey: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No' />,
    cell: ({ table, row }) =>
      table.getState().pagination.pageIndex * table.getState().pagination.pageSize + row.index + 1,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'name',
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Name' />,
    cell: ({ row }) => row.getValue('name'),
  },
  {
    id: 'description',
    accessorKey: 'description',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Deskripsi' />,
    cell: ({ row }) => row.getValue('description'),
  },
  {
    id: 'type',
    accessorKey: 'type',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Tipe' />,
    cell: ({ row }) => row.getValue('type'),
  },
  {
    id: 'category',
    accessorKey: 'category',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Kategori' />,
    cell: ({ row }) => row.getValue('category'),
  },
]
