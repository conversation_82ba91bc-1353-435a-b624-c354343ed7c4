import { Validation } from '@/constant/validation'
import { z } from 'zod'

export type NewsType = {
  id?: string
  name: string
  type: string
  category: string
  description: string
}

export const AddNewsSchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  type: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  category: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  description: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export const EditNewsSchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  type: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  category: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  description: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})
