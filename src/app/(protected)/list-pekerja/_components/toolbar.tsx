import { Input } from '@/components/ui/input'
import { useRef } from 'react'
import { MdSearch } from 'react-icons/md'

const Toolbar = ({ setFilter }: { setFilter: (value: string) => void }) => {
  const filterRef = useRef<HTMLInputElement>(null)

  let idleTimer: NodeJS.Timeout

  return (
    <div className='justify-between gap-2 md:flex'>
      <Input
        className='w-full text-sm md:w-[300px]'
        placeholder='Cari nama/no telp/email...'
        ref={filterRef}
        onChange={(e) => {
          if (idleTimer) clearTimeout(idleTimer)

          idleTimer = setTimeout(() => {
            setFilter(e.target.value)
          }, 500)
        }}
        prefixIcon={<MdSearch size={20} className='mr-2' />}
      />
    </div>
  )
}

export default Toolbar
