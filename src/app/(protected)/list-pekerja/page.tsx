'use client'

import { ActiveTab } from '@/app/(protected)/list-pekerja/_components/active-tab'
import { RejectedTab } from '@/app/(protected)/list-pekerja/_components/rejected-tab'
import { SubmissionTab } from '@/app/(protected)/list-pekerja/_components/submission-tab'
import { Card, CardContent, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

const SkillSetPage = () => {
  return (
    <Card>
      <CardContent>
        <CardTitle>List Pekerja</CardTitle>
        <Tabs defaultValue='submission'>
          <TabsList className='mt-5'>
            <TabsTrigger value='submission'>Pengajuan</TabsTrigger>
            <TabsTrigger value='active'>Aktif</TabsTrigger>
            <TabsTrigger value='rejected'>Ditolak</TabsTrigger>
          </TabsList>
          <TabsContent value={'submission'}>
            <SubmissionTab />
          </TabsContent>
          <TabsContent value={'active'}>
            <ActiveTab />
          </TabsContent>
          <TabsContent value={'rejected'}>
            <RejectedTab />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default SkillSetPage
