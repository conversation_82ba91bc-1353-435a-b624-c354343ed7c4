'use client'

import { Row } from '@/components/custom/row'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loading } from '@/components/ui/loading'
import { GET_USER } from '@/constant/api-url'
import { fetcher } from '@/lib/fetcher'
import { initialName } from '@/lib/utils'
import { useQuery } from '@tanstack/react-query'
import dayjs from 'dayjs'

const DialogContentUser = ({ id }: { id?: string }) => {
  const { data, isLoading } = useQuery({
    queryKey: ['user-detail', id],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          if (!id) return {}

          const res = await axios.get(GET_USER + '/' + id)
          return res?.data?.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  console.log({ data })

  if (isLoading) return <Loading label='Memuat data...' />

  return (
    <div>
      <div className='flex items-center gap-4'>
        <Avatar className='h-24 w-24'>
          <AvatarImage src={data?.imgProfile || '/images/common/placeholder.png'} />
          <AvatarFallback>{initialName(data?.fullname || '')}</AvatarFallback>
        </Avatar>
        <div>
          <div className='font-bold'>{data?.fullname || ''}</div>
          <div className='text-muted-foreground text-sm'>
            {dayjs(data?.memberSince).format('DD MMMM YYYY')}
          </div>
        </div>
      </div>
      <Row label={'Kewarganegaraan'} value={data?.nationality} />
      <Row label={'Email'} value={data?.email} />
      <Row label={'Tanggal Lahir'} value={dayjs(data?.dob).format('DD MMMM YYYY')} />
      <Row label={'Jenis Kelamin'} value={data?.gender === 'M' ? 'Laki laki' : 'Perempuan'} />
      <Row label={'No. Telp'} value={data?.phoneNumber} />
      <Row
        label={'Kebutuhan'}
        value={
          data?.needs?.length ? (
            <ul className='list-inside list-decimal'>
              {data?.needs?.map((d: string) => (
                <li key={d}>{d}</li>
              ))}
            </ul>
          ) : (
            '-'
          )
        }
      />
    </div>
  )
}

export default DialogContentUser
