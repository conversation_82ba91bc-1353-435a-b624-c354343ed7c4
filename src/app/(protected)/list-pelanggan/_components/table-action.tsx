import DialogContentUser from '@/app/(protected)/list-pelanggan/_components/dialog-content'
import DataTableAction from '@/components/ui/data-table-action'
import { DELETE_USER } from '@/constant/api-url'
import { useConfirm } from '@/hooks/use-confirm'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { successToast } from '@/lib/toast'
import { EyeIcon, TrashIcon } from 'lucide-react'

const TableAction = ({ data, refetch }: { data: Record<string, any>; refetch: () => void }) => {
  const { show } = useDialog()
  const confirm = useConfirm()

  return (
    <DataTableAction
      list={[
        { label: 'View', icon: <EyeIcon />, value: 'view' },
        { label: 'Delete', icon: <TrashIcon />, value: 'delete' },
      ]}
      onChange={(value) => {
        if (value === 'view') {
          show({
            title: 'Detail Pelanggan',
            content: <DialogContentUser id={data.id} />,
          })
        }
        if (value === 'delete') {
          confirm.show({
            title: `Anda ingin Menghapus data ${data.name} ?`,
            description: 'Silahkan periksa kembali sebelum menghapus data.',
            type: 'confirm',
            onConfirm: async () => {
              await fetcher({
                callback: async (axios) => {
                  await axios.delete(DELETE_USER + '/' + data.id)
                },
                errorCallback: (err, defaultHandler) => {
                  defaultHandler()
                },
              })
              successToast('Berhasil menghapus data')
              refetch()
              confirm.close()
            },
            confirmText: 'Ya, Hapus',
          })
        }
      }}
    />
  )
}

export default TableAction
