import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { ColumnDef } from '@tanstack/react-table'
import dayjs from 'dayjs'

export const userColumns: ColumnDef<Record<string, any>>[] = [
  {
    id: 'id',
    accessorKey: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No' />,
    cell: ({ table, row }) =>
      table.getState().pagination.pageIndex * table.getState().pagination.pageSize + row.index + 1,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'fullname',
    accessorKey: 'fullname',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nama' />,
    cell: ({ row }) => row.getValue('fullname'),
  },
  {
    id: 'email',
    accessorKey: 'email',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Email' />,
    cell: ({ row }) => row.getValue('email'),
  },
  {
    id: 'phoneNumber',
    accessorKey: 'phoneNumber',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No. Telp' />,
    cell: ({ row }) => row.getValue('phoneNumber'),
  },
  {
    id: 'memberSince',
    accessorKey: 'memberSince',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Member Sejak' />,
    cell: ({ row }) => dayjs(row.getValue('memberSince')).format('DD MMM YYYY'),
  },
]
