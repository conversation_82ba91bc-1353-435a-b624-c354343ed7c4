import DialogContentAdmin from '@/app/(protected)/list-admin/_components/dialog-content'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useDialog } from '@/hooks/use-dialog'
import { DownloadIcon, PlusIcon, UploadIcon } from 'lucide-react'
import { useRef } from 'react'
import { MdSearch } from 'react-icons/md'

const Toolbar = ({
  setFilter,
  refetch,
}: {
  setFilter: (value: string) => void
  refetch: () => void
}) => {
  const { show } = useDialog()
  const filterRef = useRef<HTMLInputElement>(null)

  let idleTimer: NodeJS.Timeout

  return (
    <div className='justify-between gap-2 md:flex'>
      <Input
        className='w-full text-sm md:w-[300px]'
        placeholder='Cari email...'
        ref={filterRef}
        onChange={(e) => {
          if (idleTimer) clearTimeout(idleTimer)

          idleTimer = setTimeout(() => {
            setFilter(e.target.value)
          }, 500)
        }}
        prefixIcon={<MdSearch size={20} className='mr-2' />}
      />
      <Button
        className='rounded-full'
        onClick={() =>
          show({
            title: 'Buat Admin',
            content: <DialogContentAdmin />,
            disableBackdrop: true,
            onClose: () => refetch(),
          })
        }
      >
        <PlusIcon size={18} />
        <span>Tambah Data</span>
      </Button>
    </div>
  )
}

export default Toolbar
