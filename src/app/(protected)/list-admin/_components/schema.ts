import { Validation } from '@/constant/validation'
import { z } from 'zod'

export type AdminType = {
  id?: string
  name: string
  photo: string | File
  email: string
  phoneNumber: string
  password: string
  role: string
}

export const AddAdminSchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  photo: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
  email: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .email(Validation.email),
  phoneNumber: z.string({ message: Validation.required }),
  password: z.string({ message: Validation.required }).trim().min(6, Validation.minLength(6)),
  role: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export const EditAdminSchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  photo: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
  email: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .email(Validation.email),
  phoneNumber: z.string({ message: Validation.required }),
  password: z.string({ message: Validation.required }).trim().min(6, Validation.minLength(6)),
  role: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})
