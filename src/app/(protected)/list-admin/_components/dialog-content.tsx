'use client'

import {
  AddAdminSchema,
  EditAdminSchema,
  AdminType,
} from '@/app/(protected)/list-admin/_components/schema'
import { UploadFile } from '@/components/custom/upload-file'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loading } from '@/components/ui/loading'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CREATE_ADMIN, GET_ADMIN, UPDATE_ADMIN } from '@/constant/api-url'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { successToast } from '@/lib/toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { EyeIcon, EyeOffIcon } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'

const DialogContentAdmin = ({ id, readonly }: { id?: string; readonly?: boolean }) => {
  const { hide, onClose } = useDialog()

  const [showPassword, setShowPassword] = useState(false)

  const form = useForm<AdminType>({
    resolver: zodResolver(id ? EditAdminSchema : AddAdminSchema),
    defaultValues: {
      id: id ? id : undefined,
      name: '',
      photo: '',
      email: '',
      password: '',
      role: '',
      phoneNumber: '',
    },
  })

  const { isLoading } = useQuery({
    queryKey: ['admin-detail', id],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          if (!id) return {}

          const res = await axios.get(GET_ADMIN + '/' + id)
          const data = res?.data?.data || {}

          form.reset({
            id: data.id,
            name: data.name,
            photo: data.photo,
            email: data.email,
            password: data.password,
            role: data.role,
            phoneNumber: data.phoneNumber,
          })

          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: AdminType) => {
      return fetcher({
        callback: async (axios) => {
          const headers = {
            'Content-Type': 'multipart/form-data',
          }
          let res
          if (data?.id) {
            res = await axios.put(UPDATE_ADMIN + '/' + data.id, data, { headers })
          } else {
            res = await axios.post(CREATE_ADMIN, data, { headers })
          }
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
    onSuccess: () => {
      console.log(form.getValues())
      successToast(`Berhasil ${form.getValues()?.id ? 'mengubah' : 'menambah'} data`)
      hide()
      onClose?.()
    },
  })

  const onSubmit = (data: AdminType) => {
    mutate(data)
  }

  if (isLoading) return <Loading label='Memuat data...' />

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-3'>
        <FormField
          control={form.control}
          name='photo'
          render={({ field }) => (
            <FormItem>
              <Label>Gambar</Label>
              <UploadFile
                accept='.jpg,.jpeg,.png'
                name='photo'
                onChange={field.onChange}
                classNames={{
                  container: 'h-56 mt-1',
                  image: 'h-56',
                }}
                value={field.value instanceof File ? URL.createObjectURL(field.value) : field.value}
                disabled={readonly}
                withPreview
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='name'
          disabled={readonly}
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='name' required>
                      Nama
                    </Label>
                    <Input
                      className='mt-1'
                      id='name'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='email'
          disabled={readonly}
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='email' required>
                      Email
                    </Label>
                    <Input
                      className='mt-1'
                      id='email'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='phoneNumber'
          disabled={readonly}
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='phoneNumber'>No. Telp</Label>
                    <Input
                      className='mt-1'
                      id='phoneNumber'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='password'
          disabled={readonly}
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='password' required>
                      Password
                    </Label>
                    <Input
                      className='mt-1'
                      id='password'
                      type={showPassword ? 'text' : 'password'}
                      isError={!!fieldState.error?.message}
                      suffixIcon={
                        showPassword ? (
                          <EyeOffIcon onClick={() => setShowPassword(!showPassword)} />
                        ) : (
                          <EyeIcon onClick={() => setShowPassword(!showPassword)} />
                        )
                      }
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        <FormField
          control={form.control}
          name='role'
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label required>Role</Label>
                    <Select
                      value={field.value}
                      onValueChange={(value) => field.onChange(value)}
                      disabled={readonly}
                    >
                      <SelectTrigger className='mt-1 w-full' isError={!!fieldState.error?.message}>
                        <SelectValue placeholder='Pilih Role' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='admin'>Admin</SelectItem>
                        <SelectItem value='superadmin'>Super Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        {!readonly && (
          <div className='mt-4 flex justify-end gap-2'>
            <Button type='submit' loading={isPending}>
              Simpan
            </Button>
            <Button type='button' variant='destructive' onClick={hide} loading={isPending}>
              Batal
            </Button>
          </div>
        )}
      </form>
    </Form>
  )
}

export default DialogContentAdmin
