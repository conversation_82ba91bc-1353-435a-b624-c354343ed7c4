import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { ColumnDef } from '@tanstack/react-table'
import Image from 'next/image'

export const jobCategoryColumns: ColumnDef<Record<string, any>>[] = [
  {
    id: 'id',
    accessorKey: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No' />,
    cell: ({ table, row }) =>
      table.getState().pagination.pageIndex * table.getState().pagination.pageSize + row.index + 1,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'image',
    accessorKey: 'image',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Gambar' />,
    cell: ({ row }) => (
      <Image
        src={row.getValue('image') || '/images/common/placeholder.png'}
        alt={row.getValue('name')}
        width={50}
        height={50}
        className='h-20 w-20 rounded-md object-cover object-top'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'name',
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nama' />,
    cell: ({ row }) => row.getValue('name'),
  },
  {
    id: 'isRecommendation',
    accessorKey: 'isRecommendation',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Rekomendasi' />,
    cell: ({ row }) => (row.getValue('isRecommendation') ? 'Iya' : 'Tidak'),
  },
]
