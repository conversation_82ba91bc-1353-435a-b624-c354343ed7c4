import { Validation } from '@/constant/validation'
import { z } from 'zod'

export type JobCategoryType = {
  id?: string
  name: string
  image: string | File
  isRecommendation: boolean
}

export const AddJobCategorySchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  image: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
  isRecommendation: z.boolean({ message: Validation.required }),
})

export const EditJobCategorySchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  image: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
  isRecommendation: z.boolean({ message: Validation.required }),
})
