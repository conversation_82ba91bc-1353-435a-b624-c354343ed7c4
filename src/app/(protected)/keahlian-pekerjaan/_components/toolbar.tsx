import DialogContentSkillSet from '@/app/(protected)/keahlian-pekerjaan/_components/dialog-content'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { DOWNLOAD_SKILL_SET } from '@/constant/api-url'
import { useDialog } from '@/hooks/use-dialog'
import { createDownloadMutation } from '@/lib/download-utils'
import { fetcher } from '@/lib/fetcher'
import { useMutation } from '@tanstack/react-query'
import { DownloadIcon, PlusIcon, UploadIcon } from 'lucide-react'
import { useRef } from 'react'
import { MdSearch } from 'react-icons/md'

const Toolbar = ({
  setFilter,
  refetch,
}: {
  setFilter: (value: string) => void
  refetch: () => void
}) => {
  const { show } = useDialog()
  const filterRef = useRef<HTMLInputElement>(null)

  const { mutate: handleDownload } = useMutation({
    mutationKey: ["download-skill-set"],
    mutationFn: () => createDownloadMutation(DOWNLOAD_SKILL_SET, 'skill-set-download')(fetcher),
  })

  let idleTimer: NodeJS.Timeout

  return (
    <div className='justify-between gap-2 md:flex'>
      <Input
        className='w-full text-sm md:w-[300px]'
        placeholder='Cari Keahlian Pekerjaan...'
        ref={filterRef}
        onChange={(e) => {
          if (idleTimer) clearTimeout(idleTimer)

          idleTimer = setTimeout(() => {
            setFilter(e.target.value)
          }, 500)
        }}
        prefixIcon={<MdSearch size={20} className='mr-2' />}
      />
      <div className='flex flex-row gap-2'>
        <Button className='rounded-full' onClick={() => handleDownload()}>
          <DownloadIcon size={18} />
          <span>Impor</span>
        </Button>
        <Button className='rounded-full' onClick={() => {}}>
          <UploadIcon size={18} />
          <span>Ekspor</span>
        </Button>
        <Button
          className='rounded-full'
          onClick={() =>
            show({
              title: 'Buat Kategori Pekerjaan',
              content: <DialogContentSkillSet />,
              disableBackdrop: true,
              onClose: () => refetch(),
            })
          }
        >
          <PlusIcon size={18} />
          <span>Tambah Data</span>
        </Button>
      </div>
    </div>
  )
}

export default Toolbar
