'use client'

import {
  AddSkillSetSchema,
  EditSkillSetSchema,
  SkillSetType,
} from '@/app/(protected)/keahlian-pek<PERSON><PERSON><PERSON>/_components/schema'
import { UploadFile } from '@/components/custom/upload-file'
import { Button } from '@/components/ui/button'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Loading } from '@/components/ui/loading'
import { CREATE_SKILL_SET, GET_SKILL_SET, UPDATE_SKILL_SET } from '@/constant/api-url'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { successToast } from '@/lib/toast'
import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { useForm } from 'react-hook-form'

const DialogContentSkillSet = ({ id, readonly }: { id?: string; readonly?: boolean }) => {
  const { hide, onClose } = useDialog()

  const form = useForm<SkillSetType>({
    resolver: zodResolver(id ? EditSkillSetSchema : AddSkillSetSchema),
    defaultValues: {
      id: id ? id : undefined,
      name: '',
      image: '',
    },
  })

  const { isLoading } = useQuery({
    queryKey: ['job-category-detail', id],
    queryFn: async () => {
      return fetcher({
        callback: async (axios) => {
          if (!id) return {}

          const res = await axios.get(GET_SKILL_SET + '/' + id)
          const data = res?.data?.data || {}

          form.reset({
            id: data.id,
            name: data.name,
            image: data.image,
          })

          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  const { mutate, isPending } = useMutation({
    mutationFn: async (data: SkillSetType) => {
      return fetcher({
        callback: async (axios) => {
          const headers = {
            'Content-Type': 'multipart/form-data',
          }
          let res
          if (data?.id) {
            res = await axios.put(UPDATE_SKILL_SET + '/' + data.id, data, { headers })
          } else {
            res = await axios.post(CREATE_SKILL_SET, data, { headers })
          }
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
    onSuccess: () => {
      console.log(form.getValues())
      successToast(`Berhasil ${form.getValues()?.id ? 'mengubah' : 'menambah'} data`)
      hide()
      onClose?.()
    },
  })

  const onSubmit = (data: SkillSetType) => {
    mutate(data)
  }

  if (isLoading) return <Loading label='Memuat data...' />

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className='flex flex-col gap-3'>
        <FormField
          control={form.control}
          name='image'
          render={({ field }) => (
            <FormItem>
              <Label>Gambar</Label>
              <UploadFile
                accept='.jpg,.jpeg,.png'
                name='image'
                onChange={field.onChange}
                classNames={{
                  container: 'h-56 mt-1',
                  image: 'h-56',
                }}
                value={field.value instanceof File ? URL.createObjectURL(field.value) : field.value}
                disabled={readonly}
                withPreview
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name='name'
          disabled={readonly}
          render={({ field, fieldState }) => {
            return (
              <FormItem>
                <FormControl>
                  <div>
                    <Label htmlFor='name' required>
                      Nama
                    </Label>
                    <Input
                      className='mt-1'
                      id='name'
                      isError={!!fieldState.error?.message}
                      {...field}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )
          }}
        />

        {!readonly && (
          <div className='mt-4 flex justify-end gap-2'>
            <Button type='submit' loading={isPending}>
              Simpan
            </Button>
            <Button type='button' variant='destructive' onClick={hide} loading={isPending}>
              Batal
            </Button>
          </div>
        )}
      </form>
    </Form>
  )
}

export default DialogContentSkillSet
