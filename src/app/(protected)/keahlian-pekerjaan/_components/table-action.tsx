import DialogContentSkillSet from '@/app/(protected)/keahlian-peker<PERSON>an/_components/dialog-content'
import DataTableAction from '@/components/ui/data-table-action'
import { DELETE_JOB_CATEGORY } from '@/constant/api-url'
import { useConfirm } from '@/hooks/use-confirm'
import { useDialog } from '@/hooks/use-dialog'
import { fetcher } from '@/lib/fetcher'
import { successToast } from '@/lib/toast'
import { EyeIcon, PencilIcon, TrashIcon } from 'lucide-react'

const TableAction = ({ data, refetch }: { data: Record<string, any>; refetch: () => void }) => {
  const { show } = useDialog()
  const confirm = useConfirm()

  return (
    <DataTableAction
      list={[
        { label: 'View', icon: <EyeIcon />, value: 'view' },
        { label: 'Edit', icon: <PencilIcon />, value: 'edit' },
        { label: 'Delete', icon: <TrashIcon />, value: 'delete' },
      ]}
      onChange={(value) => {
        if (value === 'view') {
          show({
            title: 'Detail Keahlian Pekerjaan',
            content: <DialogContentSkillSet id={data.id} readonly />,
            disableBackdrop: true,
          })
        }
        if (value === 'edit') {
          show({
            title: 'Ubah Keahlian Pekerjaan',
            content: <DialogContentSkillSet id={data.id} />,
            disableBackdrop: true,
            onClose: () => refetch(),
          })
        }
        if (value === 'delete') {
          confirm.show({
            title: `Anda ingin Menghapus data ${data.name} ?`,
            description: 'Silahkan periksa kembali sebelum menghapus data.',
            type: 'confirm',
            onConfirm: async () => {
              await fetcher({
                callback: async (axios) => {
                  await axios.delete(DELETE_JOB_CATEGORY + '/' + data.id)
                },
                errorCallback: (err, defaultHandler) => {
                  defaultHandler()
                },
              })
              successToast('Berhasil menghapus data')
              refetch()
              confirm.close()
            },
            confirmText: 'Ya, Hapus',
          })
        }
      }}
    />
  )
}

export default TableAction
