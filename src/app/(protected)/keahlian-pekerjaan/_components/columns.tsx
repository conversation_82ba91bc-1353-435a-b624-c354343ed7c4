import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { ColumnDef } from '@tanstack/react-table'
import Image from 'next/image'

export const skillSetColumns: ColumnDef<Record<string, any>>[] = [
  {
    id: 'id',
    accessorKey: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No' />,
    cell: ({ table, row }) =>
      table.getState().pagination.pageIndex * table.getState().pagination.pageSize + row.index + 1,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'nameId',
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nama Indonesia' />,
    cell: ({ row }) => {
      const name = row.original.name as Record<string,any>
      return name?.idn
    },
  },
  {
    id: 'nameEn',
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nama Inggris' />,
    cell: ({ row }) => {
      const name = row.original.name as Record<string,any>
      return name?.en
    },
  },
]
