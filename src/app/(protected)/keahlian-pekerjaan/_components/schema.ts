import { Validation } from '@/constant/validation'
import { z } from 'zod'

export type SkillSetType = {
  id?: string
  name: string
  image: string | File
}

export const AddSkillSetSchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  image: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
})

export const EditSkillSetSchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
  image: z
    .string({ message: Validation.required })
    .trim()
    .min(1, Validation.required)
    .or(z.instanceof(File)),
})
