'use client'

import { skillSetColumns } from '@/app/(protected)/keahlian-pekerjaan/_components/columns'
import TableAction from '@/app/(protected)/keahlian-pekerjaan/_components/table-action'
import Toolbar from '@/app/(protected)/keahlian-pekerjaan/_components/toolbar'
import BaseDialog from '@/components/custom/base-dialog'
import { Card, CardContent } from '@/components/ui/card'
import { DataTable } from '@/components/ui/data-table'
import { GET_SKILL_SET } from '@/constant/api-url'
import { usePagination } from '@/hooks/use-pagination'
import { useSorting } from '@/hooks/use-sorting'
import { fetcher } from '@/lib/fetcher'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

const SkillSetPage = () => {
  const { pagination, onPaginationChange } = usePagination()
  const { sorting, onSortingChange } = useSorting()

  const [filter, setFilter] = useState('')

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['skill-set'],
    queryFn: async () => {
      const params = {
        filter,
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sort: sorting?.map((d) => `${d.desc ? '-' : ''}${d.id}`)?.join(';'),
      }

      return fetcher({
        callback: async (axios) => {
          const res = await axios.get(GET_SKILL_SET, { params })
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  useEffect(() => {
    refetch()
  }, [filter, pagination, sorting, refetch])

  return (
    <Card className='rounded-md'>
      <CardContent>
        <Toolbar setFilter={setFilter} refetch={refetch} />
        <DataTable
          tableData={data}
          columns={skillSetColumns}
          loading={isLoading}
          paginationState={pagination}
          sortingState={sorting}
          pageHandler={onPaginationChange}
          sortingHandler={onSortingChange}
          action={{
            label: 'Aksi',
            field: (params) => <TableAction refetch={refetch} data={params} />,
          }}
        />
        <BaseDialog />
      </CardContent>
    </Card>
  )
}

export default SkillSetPage
