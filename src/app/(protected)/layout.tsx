import CustomBreadcrumb from '@/components/custom/breadcrumb'
import SidebarMenu from '@/components/custom/sidebar-menu'
import { SidebarProvider } from '@/components/ui/sidebar'

const ProtectedLayout = async ({ children }: { children: React.ReactNode }) => {
  // const session = await auth()

  // if (!session?.user?.id) redirect('/')

  return (
    <div className='flex gap-2'>
      <SidebarProvider className='flex gap-2'>
        <SidebarMenu />
        <div className='flex-1 overflow-x-auto pr-3 pl-2'>
          <CustomBreadcrumb />
          {children}
        </div>
      </SidebarProvider>
    </div>
  )
}

export default ProtectedLayout
