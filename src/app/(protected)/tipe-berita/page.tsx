'use client'

import { newsTypeColumns } from '@/app/(protected)/tipe-berita/_components/columns'
import TableAction from '@/app/(protected)/tipe-berita/_components/table-action'
import Toolbar from '@/app/(protected)/tipe-berita/_components/toolbar'
import BaseDialog from '@/components/custom/base-dialog'
import { Card, CardContent } from '@/components/ui/card'
import { DataTable } from '@/components/ui/data-table'
import { GET_NEWS_TYPE } from '@/constant/api-url'
import { usePagination } from '@/hooks/use-pagination'
import { useSorting } from '@/hooks/use-sorting'
import { fetcher } from '@/lib/fetcher'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'

const NewsTypePage = () => {
  const { pagination, onPaginationChange } = usePagination()
  const { sorting, onSortingChange } = useSorting()

  const [filter, setFilter] = useState('')

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['news-type'],
    queryFn: async () => {
      const params = {
        filter,
        page: pagination.pageIndex + 1,
        limit: pagination.pageSize,
        sort: sorting?.map((d) => `${d.desc ? '-' : ''}${d.id}`)?.join(';'),
      }

      return fetcher({
        callback: async (axios) => {
          const res = await axios.get(GET_NEWS_TYPE, { params })
          return res.data || {}
        },
        errorCallback: (err, defaultHandler) => {
          defaultHandler()
        },
      })
    },
  })

  useEffect(() => {
    refetch()
  }, [filter, pagination, sorting, refetch])

  return (
    <Card className='rounded-md'>
      <CardContent>
        <Toolbar setFilter={setFilter} refetch={refetch} />
        <DataTable
          tableData={data}
          columns={newsTypeColumns}
          loading={isLoading}
          paginationState={pagination}
          sortingState={sorting}
          pageHandler={onPaginationChange}
          sortingHandler={onSortingChange}
          action={{
            label: 'Aksi',
            field: ({ data }) => <TableAction refetch={refetch} data={data} />,
          }}
        />
        <BaseDialog />
      </CardContent>
    </Card>
  )
}

export default NewsTypePage
