import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { ColumnDef } from '@tanstack/react-table'

export const newsTypeColumns: ColumnDef<Record<string, any>>[] = [
  {
    id: 'id',
    accessorKey: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title='No' />,
    cell: ({ table, row }) =>
      table.getState().pagination.pageIndex * table.getState().pagination.pageSize + row.index + 1,
    enableSorting: false,
    enableHiding: false,
  },
  {
    id: 'name',
    accessorKey: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title='Nama' />,
    cell: ({ row }) => row.getValue('name'),
  },
]
