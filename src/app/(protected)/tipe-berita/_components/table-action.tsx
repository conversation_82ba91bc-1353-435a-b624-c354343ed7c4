import DataTableAction from '@/components/ui/data-table-action'
import { useConfirm } from '@/hooks/use-confirm'
import { useDialog } from '@/hooks/use-dialog'
import { successToast } from '@/lib/toast'
import { EyeIcon, PencilIcon, TrashIcon } from 'lucide-react'

const TableAction = ({ data, refetch }: { data: Record<string, any>; refetch: () => void }) => {
  const { show } = useDialog()
  const confirm = useConfirm()

  return (
    <DataTableAction
      list={[
        { label: 'View', icon: <EyeIcon />, value: 'view' },
        { label: 'Edit', icon: <PencilIcon />, value: 'edit' },
        { label: 'Delete', icon: <TrashIcon />, value: 'delete' },
      ]}
      onChange={(value) => {
        if (value === 'view') {
          show({
            title: 'Detail News Type',
            content: <div></div>,
            disableBackdrop: true,
          })
        }
        if (value === 'edit') {
          show({
            title: 'Edit News Type',
            content: <div></div>,
            disableBackdrop: true,
            onClose: () => refetch(),
          })
        }
        if (value === 'delete') {
          confirm.show({
            title: `Anda ingin Menghapus data ${data.name} ?`,
            description: 'Silahkan periksa kembali sebelum menghapus data.',
            type: 'confirm',
            onConfirm: () => {
              successToast('Berhasil menghapus data')
              refetch()
            },
            confirmText: 'Ya, Hapus',
          })
        }
      }}
    />
  )
}

export default TableAction
