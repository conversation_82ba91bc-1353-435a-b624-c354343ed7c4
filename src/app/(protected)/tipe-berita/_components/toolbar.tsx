import DialogContentNewsType from '@/app/(protected)/tipe-berita/_components/dialog-content'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useDialog } from '@/hooks/use-dialog'
import { DownloadIcon, ImportIcon, PlusIcon, UploadIcon } from 'lucide-react'
import { useRef } from 'react'
import { MdSearch } from 'react-icons/md'

const Toolbar = ({
  setFilter,
  refetch,
}: {
  setFilter: (value: string) => void
  refetch: () => void
}) => {
  const { show } = useDialog()
  const filterRef = useRef<HTMLInputElement>(null)

  let idleTimer: NodeJS.Timeout

  return (
    <div className='flex flex-col justify-between gap-2 md:flex-row'>
      <Input
        className='w-full text-sm md:w-[300px]'
        placeholder='Cari Nama Tipe Berita...'
        ref={filterRef}
        onChange={(e) => {
          if (idleTimer) clearTimeout(idleTimer)

          idleTimer = setTimeout(() => {
            setFilter(e.target.value)
          }, 500)
        }}
        prefixIcon={<MdSearch size={20} className='mr-2' />}
      />
      <div className='flex flex-row gap-2'>
        <Button className='rounded-full' onClick={() => {}}>
          <DownloadIcon size={18} />
          <span>Impor</span>
        </Button>
        <Button className='rounded-full' onClick={() => {}}>
          <UploadIcon size={18} />
          <span>Ekspor</span>
        </Button>
        <Button
          className='rounded-full'
          onClick={() =>
            show({
              title: 'Buat Tipe Berita',
              content: <DialogContentNewsType />,
              disableBackdrop: true,
              onClose: () => refetch(),
            })
          }
        >
          <PlusIcon size={18} />
          <span>Tambah Data</span>
        </Button>
      </div>
    </div>
  )
}

export default Toolbar
