import { Validation } from '@/constant/validation'
import { z } from 'zod'

export const AddNewsTypeSchema = z.object({
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export type AddNewsTypeSchemaType = z.infer<typeof AddNewsTypeSchema>

export const EditNewsTypeSchema = z.object({
  id: z.number({ message: Validation.required }).min(1, Validation.required),
  name: z.string({ message: Validation.required }).trim().min(1, Validation.required),
})

export type EditNewsTypeSchemaType = z.infer<typeof EditNewsTypeSchema>
