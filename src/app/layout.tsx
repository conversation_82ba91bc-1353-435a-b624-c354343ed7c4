import { ProgressBarProvider, QueryProvider } from '@/app/providers'
import ModalConfirm from '@/components/custom/confirm-dialog'
import { cn } from '@/lib/utils'
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Toaster } from 'sonner'
import './css/globals.css'

const fontSans = Inter({
  subsets: ['latin'],
  variable: '--font-sans',
})

export const metadata: Metadata = {
  title: 'Good Job',
  description: 'CMS for Good Job App.',
  icons: {
    icon: [
      {
        url: '/images/common/logo.png',
        href: '/images/common/logo.png',
      },
    ],
  },
}

export default async function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
  return (
    <html>
      <body className={cn('bg-background min-h-screen font-sans antialiased', fontSans.className)}>
        <ProgressBarProvider>
          <QueryProvider>{children}</QueryProvider>
        </ProgressBarProvider>
        <Toaster position='top-right' />
        <ModalConfirm />
      </body>
    </html>
  )
}
