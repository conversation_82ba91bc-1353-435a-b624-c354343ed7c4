import { auth } from '@/auth'
import { NextRequest, NextResponse } from 'next/server'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://api.goodjob.coniolabs.id'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params
  return handleProxyRequest(request, resolvedParams, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params
  return handleProxyRequest(request, resolvedParams, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params
  return handleProxyRequest(request, resolvedParams, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params
  return handleProxyRequest(request, resolvedParams, 'DELETE')
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const resolvedParams = await params
  return handleProxyRequest(request, resolvedParams, 'PATCH')
}

async function handleProxyRequest(
  request: NextRequest,
  params: { path: string[] },
  method: string
) {
  try {
    // Get the session for authentication
    const session = await auth()

    // Construct the target URL
    const path = params.path.join('/')
    const searchParams = request.nextUrl.searchParams.toString()
    const targetUrl = `${API_BASE_URL}/${path}${searchParams ? `?${searchParams}` : ''}`

    // Prepare headers - don't set Content-Type by default to allow proper handling
    const headers: Record<string, string> = {
      'ngrok-skip-browser-warning': '69420',
    }

    // Add authorization header if session exists
    // The token is spread into the session.user object from the JWT callback
    const token = (session?.user as any)?.accessToken || (session?.user as any)?.access_token
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers,
    }

    // Add body for POST, PUT, PATCH requests
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      try {
        const contentType = request.headers.get('content-type')

        if (contentType?.includes('multipart/form-data')) {
          // For form data, let the browser set the content-type with boundary
          const formData = await request.formData()
          requestOptions.body = formData
        } else if (contentType?.includes('application/json')) {
          // For JSON, set content-type and read as text
          headers['Content-Type'] = 'application/json'
          const body = await request.text()
          if (body) {
            requestOptions.body = body
          }
        } else {
          // For other content types, preserve the original content-type
          if (contentType) {
            headers['Content-Type'] = contentType
          }
          const body = await request.text()
          if (body) {
            requestOptions.body = body
          }
        }
      } catch (error) {
        console.error('Error reading request body:', error)
      }
    }

    console.log(`Proxying ${method} request to:`, targetUrl)
    console.log('Headers:', headers)

    // Make the request to the external API
    const response = await fetch(targetUrl, requestOptions)

    // Check if this is a file download based on content-type or content-disposition
    const contentType = response.headers.get('content-type') || ''
    const contentDisposition = response.headers.get('content-disposition') || ''
    const isFileDownload =
      contentDisposition.includes('attachment') ||
      contentType.includes('application/octet-stream') ||
      contentType.includes('application/pdf') ||
      contentType.includes('application/vnd.') ||
      contentType.includes('text/csv') ||
      contentType.includes('application/zip') ||
      contentType.includes('application/x-') ||
      path.includes('/download')

    let responseData: string | ArrayBuffer
    let nextResponse: NextResponse

    if (isFileDownload) {
      // For file downloads, preserve binary data
      responseData = await response.arrayBuffer()
      nextResponse = new NextResponse(responseData, {
        status: response.status,
        statusText: response.statusText,
      })

      console.log(`File download detected - Content-Type: ${contentType}`)
      console.log(`Response status: ${response.status}`)
    } else {
      // For regular API responses, handle as text
      responseData = await response.text()
      nextResponse = new NextResponse(responseData, {
        status: response.status,
        statusText: response.statusText,
      })

      console.log(`Response status: ${response.status}`)
      console.log('Response data:', responseData)
    }

    // Copy relevant headers from the original response
    const headersToForward = [
      'content-type',
      'content-disposition',
      'content-length',
      'cache-control',
      'etag',
      'last-modified',
    ]

    headersToForward.forEach((headerName) => {
      const headerValue = response.headers.get(headerName)
      if (headerValue) {
        nextResponse.headers.set(headerName, headerValue)
      }
    })

    // Add CORS headers
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set(
      'Access-Control-Allow-Methods',
      'GET, POST, PUT, DELETE, PATCH, OPTIONS'
    )
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')

    return nextResponse
  } catch (error) {
    console.error('Proxy error:', error)

    return NextResponse.json(
      {
        error: 'Proxy request failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        details: error,
      },
      { status: 500 }
    )
  }
}

// Handle preflight OPTIONS requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
