import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { NextRequest } from 'next/server'

const path = 'news'

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const newsStore = await getDataStore(path)

  const data = newsStore.find((d: Record<string, any>) => String(d.id) === id)

  return Response.json({ data })
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id
  const body = await req.json()

  const newsStore = await getDataStore(path)
  const dataIdx = newsStore.findIndex((d: Record<string, any>) => String(d.id) === id)

  newsStore[dataIdx] = {
    ...newsStore[dataIdx],
    ...body,
  }

  await setDataStore(path, newsStore)

  return Response.json({
    data: newsStore[dataIdx],
  })
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const newsStore = await getDataStore(path)

  const idx = newsStore.findIndex((d: Record<string, any>) => String(d.id) === id)
  const data = newsStore[idx]
  newsStore.splice(idx, 1)

  await setDataStore(path, newsStore)

  return Response.json({ data })
}
