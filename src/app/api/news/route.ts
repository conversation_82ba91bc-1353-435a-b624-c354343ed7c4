import fs from 'fs'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'
import { getDataStore, setDataStore } from '@/app/api/utils/datastore'

const path = 'news'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')
  const filter = req.nextUrl.searchParams.get('filter') || ''

  const [newsStore, newsTypeStore, newsCategoryStore] = await Promise.all([
    getDataStore(path),
    getDataStore('news-type'),
    getDataStore('news-category'),
  ])

  const total = newsStore.length
  let data = JSON.parse(JSON.stringify(newsStore))

  if (filter) {
    data = data.filter((d: Record<string, any>) =>
      d.name.toLowerCase().includes(filter.toLowerCase())
    )
  }

  data = data.splice((page - 1) * limit, limit)

  data.forEach((d: Record<string, any>) => {
    d.type = newsTypeStore.find((t: any) => String(t.id) === d.type)?.name
    d.category = newsCategoryStore.find((t: any) => String(t.id) === d.category)?.name
  })

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}

export async function POST(req: NextRequest) {
  const body = await req.json()

  const newsStore = await getDataStore(path)
  const newData = {
    id: newsStore.length > 0 ? newsStore[newsStore.length - 1].id + 1 : 1,
    name: body.name,
    type: body.type,
    description: body.description,
    category: body.category,
    createdAt: faker.date.anytime(),
  }

  newsStore.push(newData)

  await setDataStore(path, newsStore)

  return Response.json({
    data: newData,
  })
}
