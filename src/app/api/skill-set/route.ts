import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import fs from 'fs'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'

const path = 'skill-set'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')

  const skillSetStore = await getDataStore(path)

  const total = skillSetStore.length
  let data = JSON.parse(JSON.stringify(skillSetStore))
  data = data.splice((page - 1) * limit, limit)

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}

export async function POST(req: NextRequest) {
  const body = await req.formData()

  const image = body.get('image') as File

  const imagePath = '/skill-set/' + image.name

  fs.mkdirSync(process.cwd() + '/public/skill-set/', { recursive: true })
  fs.writeFileSync(process.cwd() + '/public' + imagePath, Buffer.from(await image.arrayBuffer()))

  const skillSetStore = await getDataStore(path)
  const newData = {
    id: skillSetStore.length + 1,
    name: body.get('name'),
    image: imagePath,
    createdAt: faker.date.anytime(),
  }

  skillSetStore.push(newData)

  await setDataStore(path, skillSetStore)

  return Response.json({
    data: newData,
  })
}
