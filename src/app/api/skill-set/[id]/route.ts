import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { faker } from '@faker-js/faker'
import fs from 'fs'
import { NextRequest } from 'next/server'

const path = 'skill-set'

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const skillSetStore = await getDataStore(path)

  const data = skillSetStore.find((d: Record<string, any>) => String(d.id) === id)

  return Response.json({ data })
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id
  const body = await req.formData()

  const image = body.get('image')

  if (image instanceof File) {
    const imagePath = '/skill-set/' + image.name
    fs.mkdirSync(process.cwd() + '/public/skill-set/', { recursive: true })
    fs.writeFileSync(process.cwd() + '/public' + imagePath, Buffer.from(await image.arrayBuffer()))
    body.set('image', imagePath)
  }

  const skillSetStore = await getDataStore(path)
  const dataIdx = skillSetStore.findIndex((d: Record<string, any>) => String(d.id) === id)

  const newData = {
    name: body.get('name'),
    image: body.get('image'),
    updatedAt: faker.date.anytime(),
  }

  skillSetStore[dataIdx] = {
    ...skillSetStore[dataIdx],
    ...newData,
  }

  await setDataStore(path, skillSetStore)

  return Response.json({
    data: skillSetStore[dataIdx],
  })
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const skillSetStore = await getDataStore(path)

  const idx = skillSetStore.findIndex((d: Record<string, any>) => String(d.id) === id)
  const data = skillSetStore[idx]
  skillSetStore.splice(idx, 1)

  await setDataStore(path, skillSetStore)

  return Response.json({ data })
}
