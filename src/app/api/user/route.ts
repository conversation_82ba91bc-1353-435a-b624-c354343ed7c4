import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { faker } from '@faker-js/faker'
import { NextRequest } from 'next/server'

const path = 'user'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')

  let userStore = await getDataStore(path)
  let total = userStore.length

  if (!total) {
    const mock = []
    for (let i = 0; i < 30; i++) {
      const hasNeed = faker.datatype.boolean()

      const needs = []
      if (hasNeed) {
        const limitNeed = faker.number.int({ min: 1, max: 10 })
        for (let j = 0; j < limitNeed; j++) {
          needs.push(faker.commerce.productName())
        }
      }

      mock.push({
        id: i + 1,
        fullname: faker.person.fullName(),
        dob: faker.date.past(),
        gender: faker.helpers.arrayElement(['M', 'F']),
        nationality: faker.location.country(),
        email: faker.internet.email(),
        phoneNumber: faker.phone.number({ style: 'international' }),
        imgProfile: faker.image.avatar(),
        memberSince: faker.date.past(),
        needs,
      })
    }

    await setDataStore(path, mock)
    userStore = await getDataStore(path)
    total = userStore.length
  }

  let data = JSON.parse(JSON.stringify(userStore))
  data = data.splice((page - 1) * limit, limit)

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}
