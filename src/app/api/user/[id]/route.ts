import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { NextRequest } from 'next/server'

const path = 'user'

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const userStore = await getDataStore(path)

  const data = userStore.find((d: Record<string, any>) => String(d.id) === id)

  return Response.json({ data })
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const userStore = await getDataStore(path)

  const idx = userStore.findIndex((d: Record<string, any>) => String(d.id) === id)
  const data = userStore[idx]
  userStore.splice(idx, 1)

  await setDataStore(path, userStore)

  return Response.json({ data })
}
