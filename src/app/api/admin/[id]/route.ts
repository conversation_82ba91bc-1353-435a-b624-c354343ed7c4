import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { faker } from '@faker-js/faker'
import fs from 'fs'
import { NextRequest } from 'next/server'

const path = 'admin'

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const adminStore = await getDataStore(path)

  const data = adminStore.find((d: Record<string, any>) => String(d.id) === id)

  return Response.json({ data })
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id
  const body = await req.formData()

  const photo = body.get('photo')

  if (photo instanceof File) {
    const photoPath = '/admin/' + photo.name
    fs.mkdirSync(process.cwd() + '/public/admin/', { recursive: true })
    fs.writeFileSync(process.cwd() + '/public' + photoPath, Buffer.from(await photo.arrayBuffer()))
    body.set('photo', photoPath)
  }

  const adminStore = await getDataStore(path)
  const dataIdx = adminStore.findIndex((d: Record<string, any>) => String(d.id) === id)

  const newData = {
    name: body.get('name'),
    photo: body.get('photo'),
    email: body.get('email'),
    phoneNumber: body.get('phoneNumber'),
    password: body.get('password'),
    role: body.get('role'),
    updatedAt: faker.date.anytime(),
  }

  adminStore[dataIdx] = {
    ...adminStore[dataIdx],
    ...newData,
  }

  await setDataStore(path, adminStore)

  return Response.json({
    data: adminStore[dataIdx],
  })
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const adminStore = await getDataStore(path)

  const idx = adminStore.findIndex((d: Record<string, any>) => String(d.id) === id)
  const data = adminStore[idx]
  adminStore.splice(idx, 1)

  await setDataStore(path, adminStore)

  return Response.json({ data })
}
