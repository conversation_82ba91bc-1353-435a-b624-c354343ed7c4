import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import fs from 'fs'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'

const path = 'admin'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')

  const adminStore = await getDataStore(path)

  const total = adminStore.length
  let data = JSON.parse(JSON.stringify(adminStore))
  data = data.splice((page - 1) * limit, limit)

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}

export async function POST(req: NextRequest) {
  const body = await req.formData()

  const image = body.get('photo') as File

  const imagePath = '/admin/' + image.name

  fs.mkdirSync(process.cwd() + '/public/admin/', { recursive: true })
  fs.writeFileSync(process.cwd() + '/public' + imagePath, Buffer.from(await image.arrayBuffer()))

  const adminStore = await getDataStore(path)
  const newData = {
    id: adminStore.length + 1,
    name: body.get('name'),
    photo: imagePath,
    email: body.get('email'),
    phoneNumber: body.get('phoneNumber'),
    password: body.get('password'),
    role: body.get('role'),
    createdAt: faker.date.anytime(),
  }

  adminStore.push(newData)

  await setDataStore(path, adminStore)

  return Response.json({
    data: newData,
  })
}
