import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'

const path = 'news-category'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')

  const newsCategoryStore = await getDataStore(path)

  const total = newsCategoryStore.length
  let data = JSON.parse(JSON.stringify(newsCategoryStore))
  data = data.splice((page - 1) * limit, limit)

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}

export async function POST(req: NextRequest) {
  const body = await req.json()

  const newsCategoryStore = await getDataStore(path)
  const newData = {
    id: newsCategoryStore.length + 1,
    name: body.name,
    createdAt: faker.date.anytime(),
  }

  newsCategoryStore.push(newData)

  await setDataStore(path, newsCategoryStore)

  return Response.json({
    data: newData,
  })
}
