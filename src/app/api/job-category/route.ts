import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import fs from 'fs'
import { NextRequest } from 'next/server'
import { faker } from '@faker-js/faker'

const path = 'job-category'

export async function GET(req: NextRequest) {
  const limit = Number(req.nextUrl.searchParams.get('limit') || '10')
  const page = Number(req.nextUrl.searchParams.get('page') || '1')

  const jobCategoryStore = await getDataStore(path)

  const total = jobCategoryStore.length
  let data = JSON.parse(JSON.stringify(jobCategoryStore))
  data = data.splice((page - 1) * limit, limit)

  return Response.json({
    data,
    page,
    limit: limit,
    total_data: total,
    total_pages: Math.ceil(total / limit),
  })
}

export async function POST(req: NextRequest) {
  const body = await req.formData()

  const image = body.get('image') as File

  const imagePath = '/kategori-pekerjaan/' + image.name

  fs.mkdirSync(process.cwd() + '/public/kategori-pekerjaan/', { recursive: true })
  fs.writeFileSync(process.cwd() + '/public' + imagePath, Buffer.from(await image.arrayBuffer()))

  const jobCategoryStore = await getDataStore(path)
  const newData = {
    id: jobCategoryStore.length + 1,
    name: body.get('name'),
    image: imagePath,
    isRecommendation: Boolean(body.get('isRecommendation')),
    createdAt: faker.date.anytime(),
  }

  jobCategoryStore.push(newData)

  await setDataStore(path, jobCategoryStore)

  return Response.json({
    data: newData,
  })
}
