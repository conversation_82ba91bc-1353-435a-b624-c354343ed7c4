import { getDataStore, setDataStore } from '@/app/api/utils/datastore'
import { faker } from '@faker-js/faker'
import fs from 'fs'
import { NextRequest } from 'next/server'

const path = 'job-category'

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const jobCategoryStore = await getDataStore(path)

  const data = jobCategoryStore.find((d: Record<string, any>) => String(d.id) === id)

  return Response.json({ data })
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id
  const body = await req.formData()

  const image = body.get('image')

  if (image instanceof File) {
    const imagePath = '/kategori-pekerjaan/' + image.name
    fs.mkdirSync(process.cwd() + '/public/kategori-pekerjaan/', { recursive: true })
    fs.writeFileSync(process.cwd() + '/public' + imagePath, Buffer.from(await image.arrayBuffer()))
    body.set('image', imagePath)
  }

  const jobCategoryStore = await getDataStore(path)
  const dataIdx = jobCategoryStore.findIndex((d: Record<string, any>) => String(d.id) === id)

  const newData = {
    name: body.get('name'),
    image: body.get('image'),
    isRecommendation: body.get('isRecommendation'),
    updatedAt: faker.date.anytime(),
  }

  jobCategoryStore[dataIdx] = {
    ...jobCategoryStore[dataIdx],
    ...newData,
  }

  await setDataStore(path, jobCategoryStore)

  return Response.json({
    data: jobCategoryStore[dataIdx],
  })
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const id = (await params).id

  const jobCategoryStore = await getDataStore(path)

  const idx = jobCategoryStore.findIndex((d: Record<string, any>) => String(d.id) === id)
  const data = jobCategoryStore[idx]
  jobCategoryStore.splice(idx, 1)

  await setDataStore(path, jobCategoryStore)

  return Response.json({ data })
}
