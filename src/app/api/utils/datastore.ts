import fs from 'fs'

export const getDataStore = async (data: string) => {
  const path = process.cwd() + '/datastore/' + data + '.json'
  if (!fs.existsSync(path)) {
    fs.writeFileSync(path, JSON.stringify([]))
  }

  return JSON.parse(fs.readFileSync(path, 'utf8'))
}

export const setDataStore = async (data: string, value: Record<string, any>) => {
  const path = process.cwd() + '/datastore/' + data + '.json'
  fs.writeFileSync(path, JSON.stringify(value, null, 2))
}