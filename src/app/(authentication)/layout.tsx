import Image from 'next/image'

export default function AuthenticationLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <Image
              src="/images/common/logo.png"
              alt="Good Job Logo"
              width={80}
              height={80}
              className="rounded-lg"
              priority
            />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Good Job</h1>
          <p className="text-gray-600">Content Management System</p>
        </div>
        {children}
      </div>
    </div>
  )
}