'use client'

import { ReactNode } from 'react'
import { create } from 'zustand/react'

type OpenType = {
  open: boolean
}

type DialogDataType = {
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  title: string
  content: ReactNode
  disableBackdrop?: boolean
  withClose?: boolean
  onClose?: () => void
}

type DialogType = {
  toggle: () => void
  show: (params: DialogDataType) => void
  hide: () => void
} & DialogDataType &
  OpenType

const initialDialogData: DialogDataType = {
  size: 'md',
  title: '',
  content: null,
  disableBackdrop: false,
  withClose: true,
}

export const useDialog = create<DialogType>()((set) => ({
  ...initialDialogData,
  open: false,
  toggle: () => set((state) => ({ ...initialDialogData, open: !state.open })),
  show: (params: DialogDataType) => set(() => ({ ...params, open: true })),
  hide: () => set(() => ({ ...initialDialogData, open: false })),
}))
