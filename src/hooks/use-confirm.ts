import React from 'react'
import { create } from 'zustand'

type MessageConfirmType = {
  confirmText?: string
  cancelText?: string
  description?: string
  onConfirm?: () => void
  title?: string
  type?: 'confirm' | 'notice-success' | 'notice-error' | 'loading'
  duration?: number
  icon?: React.ReactNode
  iconStyle?: string
  confirmButtonStyle?: string
  confirmButtonVariant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
}
type ConfirmPortalType = {
  close: () => void
  isOpen: boolean
  isLoading?: boolean
  setLoading: (args: boolean) => void
  message: MessageConfirmType
  show: (args: MessageConfirmType) => void
  toogle: () => void
  toogleSubmit: () => void
}

const initialMessage: MessageConfirmType = {
  confirmText: 'Confirm',
  cancelText: 'Tidak',
  description: '',
  onConfirm: () => {},
  title: '',
  type: 'confirm',
}

export const useConfirm = create<ConfirmPortalType>()((set) => ({
  close: () =>
    set(() => ({
      isOpen: false,
      message: initialMessage,
    })),
  isOpen: false,
  message: initialMessage,
  isLoading: false,
  show: (args: MessageConfirmType) =>
    set(() => ({
      isOpen: true,
      message: args,
    })),
  setLoading: (args: boolean) =>
    set(() => ({
      isLoading: args,
    })),
  toogle: () =>
    set((state) => ({
      isOpen: !state.isOpen,
    })),
  toogleSubmit: () => set((state) => ({ isLoading: !state.isLoading })),
}))
