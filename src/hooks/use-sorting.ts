'use client'

import { useState } from 'react'

export interface ISorting {
  id: string
  desc: boolean
}

export function useSorting(initialField = '_id', initialOrder = 'desc') {
  const [sorting, setSorting] = useState<Array<ISorting>>([
    { id: initialField, desc: initialOrder === 'desc' },
  ])

  const resetSorting = () => {
    setSorting([{ id: initialField, desc: initialOrder === 'desc' }])
  }

  return {
    sorting,
    onSortingChange: setSorting,
    order: !sorting.length ? initialOrder : sorting[0].desc ? 'desc' : 'asc',
    field: sorting.length ? sorting[0].id : undefined,
    setSorting,
    resetSorting,
  }
}
