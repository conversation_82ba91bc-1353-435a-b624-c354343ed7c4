import { errorToast } from '@/lib/toast'
import axios, { AxiosInstance } from 'axios'
import { signOut } from 'next-auth/react'

const instance = () => {
  const axiosInstance = axios.create({
    baseURL: '/api/proxy',
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // The proxy will handle authentication, so we just pass through the request
  axiosInstance.interceptors.request.use(async (request) => {
    return request
  })

  axiosInstance.interceptors.response.use(
    function (response) {
      return response
    },
    async function (error) {
      const statusCode = error.response?.status

      if (statusCode === 401) {
        await signOut()
        window.location.reload()
        return
      }

      return Promise.reject(error?.response?.data || error?.response || error?.message)
    }
  )

  return axiosInstance
}

interface IProps {
  callback: (instance: AxiosInstance) => Promise<any>
  errorCallback?: (error: any, defaultHandler: () => void) => void
}

export const fetcher = async ({ callback, errorCallback }: IProps) => {
  try {
    return await callback(instance())
  } catch (err) {
    if (errorCallback) {
      return errorCallback(err, () => {
        errorToast('Terjadi kesalahan. <PERSON>lakan coba lagi.')
        throw err
      })
    }
    errorToast('Terjadi kesalahan. Silakan coba lagi.')
  }
}
