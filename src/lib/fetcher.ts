// import { auth, signOut } from '@/lib/auth'
import { auth } from '@/auth'
import { BYPASS_SESSION_URL } from '@/constant/api-url'
import { errorToast } from '@/lib/toast'
import axios, { AxiosInstance } from 'axios'
import { getSession, signOut } from 'next-auth/react'

const instance = () => {
  const axiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': '69420',
    },
  })

  axiosInstance.interceptors.request.use(async (request) => {
    try {
      let session: any = null

      if (!BYPASS_SESSION_URL.includes(request.url as string)) {
        if (typeof window !== 'undefined') {
          session = await auth()
        } else {
          session = await getSession()
        }
      }

      const token = session?.user?.['access_token'] ?? ''

      console.log({ token, session })

      if (token) {
        request.headers.Authorization = `Bearer ${token}`
      }
      return request
    } catch (_) {
      console.error('Could not intercept request with authentication')
      return request
    }
  })

  axiosInstance.interceptors.response.use(
    function (response) {
      return response
    },
    async function (error) {
      const statusCode = error.response?.status

      if (statusCode === 401) {
        await signOut()
        window.location.reload()
        return
      }

      return Promise.reject(error?.response?.data || error?.response || error?.message)
    }
  )

  return axiosInstance
}

interface IProps {
  callback: (instance: AxiosInstance) => Promise<any>
  errorCallback?: (error: any, defaultHandler: () => void) => void
}

export const fetcher = async ({ callback, errorCallback }: IProps) => {
  try {
    return await callback(instance())
  } catch (err) {
    if (errorCallback) {
      return errorCallback(err, () => {
        errorToast('Terjadi kesalahan. Silakan coba lagi.')
        throw err
      })
    }
    errorToast('Terjadi kesalahan. Silakan coba lagi.')
  }
}
