/**
 * Utility functions for handling file downloads
 */

/**
 * Triggers a file download from a blob response
 * @param blob - The blob data to download
 * @param contentDisposition - The Content-Disposition header from the response
 * @param defaultFilename - Default filename if none can be extracted from headers
 */
export function triggerDownload(
  blob: Blob,
  contentDisposition?: string,
  defaultFilename: string = 'download'
): void {
  // Create blob URL
  const url = window.URL.createObjectURL(blob)
  
  // Extract filename from Content-Disposition header or use default
  let filename = defaultFilename
  
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
    if (filenameMatch && filenameMatch[1]) {
      filename = filenameMatch[1].replace(/['"]/g, '')
    }
  }
  
  // Create temporary anchor element and trigger download
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  
  // Cleanup
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

/**
 * Creates a download mutation function for use with React Query
 * @param downloadUrl - The API endpoint for downloading
 * @param defaultFilename - Default filename if none can be extracted from headers
 * @returns A function that can be used as mutationFn in useMutation
 */
export function createDownloadMutation(
  downloadUrl: string,
  defaultFilename: string = 'download'
) {
  return async (fetcher: any) => {
    return fetcher({
      callback: async (axios: any) => {
        const res = await axios.get(downloadUrl, { responseType: 'blob' })
        
        // Create blob and trigger download
        const blob = new Blob([res.data])
        const contentDisposition = res.headers['content-disposition'] || ''
        
        triggerDownload(blob, contentDisposition, defaultFilename)
        
        return res.data || {}
      },
      errorCallback: (_err: any, defaultHandler: () => void) => {
        defaultHandler()
      },
    })
  }
}
