import { toast } from 'sonner'

const DEFAULT_DURATION = 3000

export const errorToast = (message: string, duration = DEFAULT_DURATION) => {
  return toast.error(message, {
    duration,
    dismissible: true,
    classNames: {
      toast: '!bg-red-500',
      title: '!text-white',
      description: '!text-white',
      icon: '!text-white',
    },
  })
}

export const successToast = (message: string, duration = DEFAULT_DURATION) => {
  return toast.success(message, {
    duration,
    dismissible: true,
    classNames: {
      toast: '!bg-green-500',
      title: '!text-white',
      description: '!text-white',
      icon: '!text-white',
    },
  })
}

export const infoToast = (message: string, duration = DEFAULT_DURATION) => {
  return toast.info(message, {
    duration,
    dismissible: true,
  })
}
