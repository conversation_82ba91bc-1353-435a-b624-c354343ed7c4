import 'next-auth'
import { DefaultSession } from 'next-auth'
import 'next-auth/jwt'

interface user {
  id: string
  company: string
  email: string
  phone: string
  name: string
  status: string
  isEmailVerified: boolean
  isPhoneVerified: boolean
  createdAt: string
  updatedAt: string
  roles: string[]
}

declare module 'next-auth/jwt' {
  interface JWT {
    accessToken?: string
    expiredAt: string
    refreshToken: string
    refreshTokenExpired: string
  }
}

declare module 'next-auth' {
  interface User {
    accessToken?: string
    expiredAt: string
    refreshToken: string
    refreshTokenExpired: string
    iat: number
    exp: number
    jti: string
    sub: string
    user: user
  }

  interface Session {
    user: user & DefaultSession['user']
  }
}
