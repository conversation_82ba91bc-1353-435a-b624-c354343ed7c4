# Login System Test Guide

## Test Steps

### 1. Access the Application
- Open browser and navigate to `http://localhost:3001`
- Should automatically redirect to `/login` page

### 2. Login Page UI Test
- Verify the login form displays correctly with:
  - Email input field
  - Password input field with toggle visibility button
  - Login button
  - Good Job CMS branding and logo
  - Responsive design on different screen sizes

### 3. Form Validation Test
- Try submitting empty form - should show validation errors
- Enter invalid email format - should show email validation error
- Enter password less than 6 characters - should show password length error

### 4. Authentication Test
**Note**: Since we don't have access to the actual API, the login will fail with API errors, but we can verify:
- Form submission triggers loading state
- Error handling displays appropriate messages
- Network request is made to the correct endpoint

### 5. Navigation Test
- After successful login (if API is available), should redirect to `/berita`
- Protected routes should redirect to login when not authenticated
- Logout functionality should work from sidebar

## Expected API Endpoint
The login form will send POST request to:
`https://api.goodjob.coniolabs.id/api/account/admin/login`

With body:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

## Environment Variables
Ensure `.env.local` contains:
```
NEXT_PUBLIC_API_BASE_URL=https://api.goodjob.coniolabs.id
AUTH_SECRET=a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2
NEXTAUTH_URL=http://localhost:3001
```
